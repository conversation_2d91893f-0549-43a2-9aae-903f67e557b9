#!/home/<USER>/Desktop/soup/.venv/bin/python3
# -*- coding: utf-8 -*-
# Copyright (C) 2006 <PERSON><PERSON><PERSON>, European Environment Agency
#
# This is free software.  You may redistribute it under the terms
# of the Apache license and the GNU General Public License Version
# 2 or at your option any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public
# License along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
#
# Contributor(s):
#
from __future__ import print_function
from odf.odf2xhtml import ODF2XHTML
import zipfile
import sys
#from time import gmtime, strftime

from email.mime.multipart import MIMEMultipart
from email.mime.nonmultipart import MIMENonMultipart
from email.mime.text import MIMEText
from email import encoders

if sys.version_info[0]==3: unicode=str

if len(sys.argv) != 2:
    sys.stderr.write("Usage: %s inputfile\n" % sys.argv[0])
    sys.exit(1)

suffices = {
 'wmf':('image','x-wmf'),
 'png':('image','png'),
 'gif':('image','gif'),
 'jpg':('image','jpeg'),
 'jpeg':('image','jpeg')
 }

msg = MIMEMultipart('related',type="text/html")
#   msg['Subject'] = 'Subject here'
#   msg['From'] = '<Saved by ODT2MHT>'
#   msg['Date'] = strftime("%a, %d %b %Y %H:%M:%S +0000", gmtime())
msg.preamble = 'This is a multi-part message in MIME format.'
msg.epilogue = ''
odhandler = ODF2XHTML()
result = odhandler.odf2xhtml(unicode(sys.argv[1]))
htmlpart = MIMEText(result,'html','us-ascii')
htmlpart['Content-Location'] = 'index.html'
msg.attach(htmlpart)
z = zipfile.ZipFile(sys.argv[1])
for file in z.namelist():
    if file[0:9] == 'Pictures/':
        suffix = file[file.rfind(".")+1:]
        main,sub = suffices.get(suffix,('application','octet-stream')) 
        img = MIMENonMultipart(main,sub)
        img.set_payload(z.read(file))
        img['Content-Location'] = "" + file
        encoders.encode_base64(img)
        msg.attach(img)
z.close()
print (msg.as_string())


# Local Variables: ***
# mode: python     ***
# End:             ***
