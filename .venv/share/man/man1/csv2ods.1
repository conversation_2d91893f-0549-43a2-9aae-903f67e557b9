.\"     Title: csv2ods
.\"    Author: <PERSON><PERSON><PERSON> <a<PERSON><PERSON><PERSON><PERSON> at gmail.com>
.\" Generator: DocBook XSL Stylesheets v1.74.0 <http://docbook.sf.net/>
.\"      Date: 01/04/2009
.\"    Manual: User commands
.\"    Source: odfpy
.\"  Language: English
.\"
.TH "CSV2ODS" "1" "01/04/2009" "odfpy" "User commands"
.\" -----------------------------------------------------------------
.\" * (re)Define some macros
.\" -----------------------------------------------------------------
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" toupper - uppercase a string (locale-aware)
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.de toupper
.tr aAbBcCdDeEfFgGhHiIjJkKlLmMnNoOpPqQrRsStTuUvVwWxXyYzZ
\\$*
.tr aabbccddeeffgghhiijjkkllmmnnooppqqrrssttuuvvwwxxyyzz
..
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" SH-xref - format a cross-reference to an SH section
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.de SH-xref
.ie n \{\
.\}
.toupper \\$*
.el \{\
\\$*
.\}
..
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" SH - level-one heading that works better for non-TTY output
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.de1 SH
.\" put an extra blank line of space above the head in non-TTY output
.if t \{\
.sp 1
.\}
.sp \\n[PD]u
.nr an-level 1
.set-an-margin
.nr an-prevailing-indent \\n[IN]
.fi
.in \\n[an-margin]u
.ti 0
.HTML-TAG ".NH \\n[an-level]"
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
\." make the size of the head bigger
.ps +3
.ft B
.ne (2v + 1u)
.ie n \{\
.\" if n (TTY output), use uppercase
.toupper \\$*
.\}
.el \{\
.nr an-break-flag 0
.\" if not n (not TTY), use normal case (not uppercase)
\\$1
.in \\n[an-margin]u
.ti 0
.\" if not n (not TTY), put a border/line under subheading
.sp -.6
\l'\n(.lu'
.\}
..
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" SS - level-two heading that works better for non-TTY output
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.de1 SS
.sp \\n[PD]u
.nr an-level 1
.set-an-margin
.nr an-prevailing-indent \\n[IN]
.fi
.in \\n[IN]u
.ti \\n[SN]u
.it 1 an-trap
.nr an-no-space-flag 1
.nr an-break-flag 1
.ps \\n[PS-SS]u
\." make the size of the head bigger
.ps +2
.ft B
.ne (2v + 1u)
.if \\n[.$] \&\\$*
..
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" BB/BE - put background/screen (filled box) around block of text
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.de BB
.if t \{\
.sp -.5
.br
.in +2n
.ll -2n
.gcolor red
.di BX
.\}
..
.de EB
.if t \{\
.if "\\$2"adjust-for-leading-newline" \{\
.sp -1
.\}
.br
.di
.in
.ll
.gcolor
.nr BW \\n(.lu-\\n(.i
.nr BH \\n(dn+.5v
.ne \\n(BHu+.5v
.ie "\\$2"adjust-for-leading-newline" \{\
\M[\\$1]\h'1n'\v'+.5v'\D'P \\n(BWu 0 0 \\n(BHu -\\n(BWu 0 0 -\\n(BHu'\M[]
.\}
.el \{\
\M[\\$1]\h'1n'\v'-.5v'\D'P \\n(BWu 0 0 \\n(BHu -\\n(BWu 0 0 -\\n(BHu'\M[]
.\}
.in 0
.sp -.5v
.nf
.BX
.in
.sp .5v
.fi
.\}
..
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.\" BM/EM - put colored marker in margin next to block of text
.\" ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
.de BM
.if t \{\
.br
.ll -2n
.gcolor red
.di BX
.\}
..
.de EM
.if t \{\
.br
.di
.ll
.gcolor
.nr BH \\n(dn
.ne \\n(BHu
\M[\\$1]\D'P -.75n 0 0 \\n(BHu -(\\n[.i]u - \\n(INu - .75n) 0 0 -\\n(BHu'\M[]
.in 0
.nf
.BX
.in
.fi
.\}
..
.\" -----------------------------------------------------------------
.\" * set default formatting
.\" -----------------------------------------------------------------
.\" disable hyphenation
.nh
.\" disable justification (adjust text to left margin only)
.ad l
.\" -----------------------------------------------------------------
.\" * MAIN CONTENT STARTS HERE *
.\" -----------------------------------------------------------------
.SH "Name"
csv2ods \- Create OpenDocument spreadsheet from comma separated values
.SH "Synopsis"
.fam C
.HP \w'\fBcsv2ods\fR\ 'u
\fBcsv2ods\fR \-i\ \fIfile\&.csv\fR \-o\ \fIfile\&.ods\fR
.fam
.SH "Description"
.PP
This program reads a file in CSV format \- table of columns delimited by commas, tabs or any other character\&. It then creates a spreadsheet\&. If a value looks like a number the cell is formatted as a number as well\&.
.SH "Options"
.PP
\-\-version
.RS 4
Show program\'s version number and exit
.RE
.PP
\-h, \-\-help
.RS 4
Show help message and exit
.RE
.PP
\-i \fIINPUT\fR, \-\-input=\fIINPUT\fR
.RS 4
File input in csv\&.
.RE
.PP
\-o \fIOUTPUT\fR, \-\-output=\fIOUTPUT\fR
.RS 4
File output in ods\&.
.RE
.PP
\-d \fIDELIMITER\fR, \-\-delimiter=\fIDELIMITER\fR
.RS 4
Specifies a one\-character string to use as the field separator\&. It defaults to ","\&.
.RE
.PP
\-c \fIENCODING\fR, \-\-encoding=\fIENCODING\fR
.RS 4
Specifies the encoding the file csv\&. It defaults to utf\-8\&.
.RE
.PP
\-t \fITABLENAME\fR, \-\-table=\fITABLENAME\fR
.RS 4
The table name in the output file\&.
.RE
.PP
\-s \fISKIPINITIALSPACE\fR, \-\-skipinitialspace=\fISKIPINITIALSPACE\fR
.RS 4
Specifies how to interpret whitespace which immediately follows a delimiter\&. It defaults to False, which means that whitespace immediately following a delimiter is part of the following field\&.
.RE
.PP
\-l \fILINETERMINATOR\fR, \-\-lineterminator=\fILINETERMINATOR\fR
.RS 4
Specifies the character sequence which should terminate rows\&.
.RE
.PP
\-q \fIQUOTING\fR, \-\-quoting=\fIQUOTING\fR
.RS 4
It can take on any of the following module constants: 0 = QUOTE_MINIMAL means only when required, for example, when a field contains either the quotechar or the delimiter\&. 1 = QUOTE_ALL means that quotes are always placed around fields\&. 2 = QUOTE_NONNUMERIC means that quotes are always placed around fields which do not parse as integers or floating point numbers\&. 3 = QUOTE_NONE means that quotes are never placed around fields\&. It defaults is QUOTE_MINIMAL\&.
.RE
.PP
\-e \fIESCAPECHAR\fR, \-\-escapechar=\fIESCAPECHAR\fR
.RS 4
Specifies a one\-character string used to escape the delimiter when quoting is set to QUOTE_NONE\&.
.RE
.PP
\-r \fIQUOTECHAR\fR, \-\-quotechar=\fIQUOTECHAR\fR
.RS 4
Specifies a one\-character string to use as the quoting character\&. It defaults to "\&.
.RE
.SH "Example"
.sp
.if n \{\
.RS 4
.\}
.fam C
.ps -1
.nf
.if t \{\
.sp -1
.\}
.BB lightgray adjust-for-leading-newline
.sp -1

csv2ods \-i /etc/passwd \-o accounts\&.odt \-d:
.EB lightgray adjust-for-leading-newline
.if t \{\
.sp 1
.\}
.fi
.fam
.ps +1
.if n \{\
.RE
.\}
.SH "Author"
.PP
\fBAgustin Henze\fR <\&agustinhenze at gmail\&.com\&>
.RS 4
Original author of csv\-ods\&.py
.RE
