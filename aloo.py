import os
import sys
import time
import base64
import io
import logging
import traceback
from pathlib import Path
from typing import List, Literal, Optional

from fastapi import FastAPI, Body, UploadFile, File, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
import uvicorn

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

import pandas as pd

# --------------------------
# Logging
# --------------------------
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[logging.<PERSON><PERSON>andler("server.log"), logging.<PERSON>Handler()],
)

# --------------------------
# FastAPI Setup
# --------------------------
app = FastAPI(title="Tele Taleem PowerBill API", version="3.10")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # ⚠️ restrict in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --------------------------
# File Paths
# --------------------------
# (kept your original folder name even with the typo so your environment continues to work)
ref_file_path = Path.home() / "Desktop" / "refrence_numbers" / "ref_numbers.txt"
ref_file_path.parent.mkdir(parents=True, exist_ok=True)
if not ref_file_path.exists():
    ref_file_path.touch()

desktop_path = Path.home() / "Desktop"
download_dir = desktop_path / "IESCO_Bills"
download_dir.mkdir(parents=True, exist_ok=True)

# Serve static files (bills)
app.mount("/static", StaticFiles(directory=download_dir), name="static")


# --------------------------
# Selenium helpers
# --------------------------
def get_chrome_driver(headless: bool = True) -> webdriver.Chrome:
    """
    Hardened headless Chrome:
      - Spoof UA, remove automation flags
      - Disable features that cause redirect/cookie issues
    """
    options = Options()
    if headless:
        options.add_argument("--headless=new")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-gpu")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument("--disable-popup-blocking")
    options.add_argument("--disable-notifications")
    options.add_argument("--window-size=1920,1080")
    options.add_argument("--start-maximized")
    options.add_argument("--disable-features=TranslateUI")
    # Cookies/redirect loop mitigations
    options.add_argument("--disable-features=SameSiteByDefaultCookies,CookiesWithoutSameSiteMustBeSecure")
    # Make it look less like automation
    options.add_experimental_option("excludeSwitches", ["enable-automation"])
    options.add_experimental_option("useAutomationExtension", False)
    options.add_argument(
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    )

    # Keep downloads in-memory; we use printToPDF
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=options)

    # Remove webdriver flag at runtime
    try:
        driver.execute_cdp_cmd(
            "Page.addScriptToEvaluateOnNewDocument",
            {
                "source": """
                  Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                """
            },
        )
    except Exception:
        pass

    return driver


def wait_ready(driver: webdriver.Chrome, timeout: int = 20):
    WebDriverWait(driver, timeout).until(
        lambda d: d.execute_script("return document.readyState") == "complete"
    )


def try_click(driver: webdriver.Chrome, by: By, sel: str, timeout: int = 10) -> bool:
    try:
        el = WebDriverWait(driver, timeout).until(EC.element_to_be_clickable((by, sel)))
        el.click()
        return True
    except Exception:
        return False


def accept_cookies_if_any(driver: webdriver.Chrome):
    # Common cookie banners; ignore errors
    selectors = [
        (By.XPATH, "//button[contains(., 'Accept')]"),
        (By.XPATH, "//button[contains(., 'I agree')]"),
        (By.CSS_SELECTOR, "button#onetrust-accept-btn-handler"),
        (By.CSS_SELECTOR, "button[aria-label='accept cookies']"),
    ]
    for by, sel in selectors:
        if try_click(driver, by, sel, timeout=3):
            logging.info("🍪 Cookie banner accepted")
            break


def enable_print_domain(driver: webdriver.Chrome):
    # Make sure Page domain is enabled to avoid CDP errors
    try:
        driver.execute_cdp_cmd("Page.enable", {})
    except Exception:
        pass


def save_pdf(driver: webdriver.Chrome, save_path: Path):
    """
    printToPDF returns base64-encoded PDF data in 'data' key.
    """
    try:
        enable_print_domain(driver)
        pdf_obj = driver.execute_cdp_cmd(
            "Page.printToPDF",
            {
                "printBackground": True,
                "preferCSSPageSize": True,
                "paperWidth": 8.27,   # A4
                "paperHeight": 11.69, # A4
                "marginTop": 0.2,
                "marginBottom": 0.2,
                "marginLeft": 0.2,
                "marginRight": 0.2,
            },
        )
        raw = pdf_obj.get("data")
        if not raw:
            raise RuntimeError("printToPDF returned no data")
        pdf_bytes = base64.b64decode(raw)
        with open(save_path, "wb") as f:
            f.write(pdf_bytes)
        logging.info(f"✅ Saved PDF: {save_path}")
    except Exception as e:
        logging.error(f"❌ Failed to save PDF {save_path}: {e}")
        raise


def open_bill_popup(driver: webdriver.Chrome):
    """
    Click the 'Open Bill' button after 'Check Bill'.
    The site can render different markup; we try a few selectors.
    """
    candidates = [
        (By.CSS_SELECTOR, "form#billForm button[type='submit']"),
        (By.XPATH, "//form[@id='billForm']//button[@type='submit']"),
        (By.XPATH, "//button[contains(., 'Open Bill')]"),
        (By.XPATH, "//button[contains(@class,'btn') and contains(., 'Open')]"),
    ]
    for by, sel in candidates:
        if try_click(driver, by, sel, timeout=12):
            return True
    return False


def switch_to_new_window(driver: webdriver.Chrome, timeout: int = 15):
    WebDriverWait(driver, timeout).until(lambda d: len(d.window_handles) > 1)
    driver.switch_to.window(driver.window_handles[-1])


def wait_bill_render(driver: webdriver.Chrome):
    """
    Wait for something that indicates the bill page really loaded.
    """
    candidates = [
        (By.CSS_SELECTOR, "div.bill-container"),
        (By.CSS_SELECTOR, "div.container"),
        (By.TAG_NAME, "body"),
    ]
    for by, sel in candidates:
        try:
            WebDriverWait(driver, 25).until(EC.presence_of_element_located((by, sel)))
            return
        except Exception:
            continue
    # If none matched, still give DOM a heartbeat
    time.sleep(2)


def clear_site_data(driver: webdriver.Chrome, origin: str = "https://iescobill.pk"):
    """
    Clear cookies/storage for origin to avoid redirect loops caused by bad cookies.
    """
    try:
        driver.execute_cdp_cmd("Storage.clearDataForOrigin", {"origin": origin, "storageTypes": "all"})
    except Exception:
        pass


# --------------------------
# Core task: download a single bill
# --------------------------
def download_single_bill(driver: webdriver.Chrome, ref_number: str) -> Path:
    url = "https://iescobill.pk/"
    clear_site_data(driver, origin="https://iescobill.pk")
    driver.get(url)
    wait_ready(driver)
    accept_cookies_if_any(driver)

    # Find reference input
    ref_input = WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, "reference")))
    ref_input.clear()
    ref_input.send_keys(ref_number)

    # Click 'Check Bill'
    check_clicked = try_click(driver, By.ID, "checkBill", timeout=10)
    if not check_clicked:
        raise RuntimeError("Could not click 'Check Bill'")

    # Give backend a moment
    time.sleep(1.5)

    # Click 'Open Bill'
    if not open_bill_popup(driver):
        raise RuntimeError("Could not click 'Open Bill'")

    # Switch to popup/new tab
    switch_to_new_window(driver, timeout=15)
    wait_ready(driver)
    wait_bill_render(driver)

    # Save as PDF
    save_path = download_dir / f"{ref_number}.pdf"
    save_pdf(driver, save_path)
    return save_path


# --------------------------
# API: Upload Excel / CSV / ODS
# --------------------------
@app.post("/api/upload-excel")
async def upload_excel(file: UploadFile = File(...)):
    fname = (file.filename or "").lower()
    ext = Path(fname).suffix

    try:
        contents = await file.read()
        buffer = io.BytesIO(contents)

        # --- Handle file formats ---
        if ext == ".csv":
            df = pd.read_csv(buffer)

        elif ext == ".xlsx":
            df = pd.read_excel(buffer, engine="openpyxl")

        elif ext == ".xls":
            # xlrd>=2.0 dropped xls; if not installed, pandas will error; that’s OK—user will see a clear message
            df = pd.read_excel(buffer, engine="xlrd")

        elif ext == ".ods":
            df = pd.read_excel(buffer, engine="odf")

        else:
            raise ValueError(f"Unsupported file type: {ext}. Please upload CSV, XLS, XLSX, or ODS.")

        # --- Extract references (first column only) ---
        references = (
            df.iloc[:, 0]
            .dropna()
            .astype(str)
            .str.replace(r"\s+", "", regex=True)
            .tolist()
        )

        with open(ref_file_path, "w") as f:
            f.writelines(ref + "\n" for ref in references)

        logging.info(f"✅ Uploaded {fname}, refs={len(references)}")
        return {
            "message": "✅ File uploaded successfully",
            "total_refs": len(references),
            "preview": references[:5],
        }

    except Exception as e:
        logging.error(f"❌ Upload failed: {e}")
        logging.debug(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": str(e)})


# --------------------------
# API: Run all bills with retries
# --------------------------
@app.get("/api/run-file")
async def run_file_bills(headless: bool = Query(True)):
    with open(ref_file_path, "r") as f:
        ref_numbers = [line.strip() for line in f if line.strip()]

    if not ref_numbers:
        return JSONResponse(status_code=400, content={"error": "No reference numbers found"})

    results: List[dict] = []

    for ref in ref_numbers:
        attempt = 0
        max_attempts = 2
        while attempt < max_attempts:
            attempt += 1
            driver = None
            try:
                driver = get_chrome_driver(headless=headless)
                save_path = download_single_bill(driver, ref)
                results.append(
                    {
                        "refNumber": ref,
                        "status": f"✅ Completed (attempt {attempt})",
                        "file": f"/static/{save_path.name}",
                    }
                )
                break  # success
            except Exception as e:
                msg = str(e)
                logging.error(f"❌ Failed {ref} on attempt {attempt}: {msg}")
                logging.debug(traceback.format_exc())
                if attempt >= max_attempts:
                    results.append(
                        {
                            "refNumber": ref,
                            "status": f"❌ Failed after {attempt} attempts: {msg}",
                        }
                    )
            finally:
                if driver:
                    try:
                        driver.quit()
                    except Exception:
                        pass

    return {"results": results}


# --------------------------
# API: Run single bill
# --------------------------
@app.post("/api/run-single")
async def run_single_bill(
    payload: dict = Body(...),
    headless: bool = Query(True),
):
    ref_number = payload.get("refNumber")
    if not ref_number:
        return JSONResponse(status_code=400, content={"error": "No reference number provided"})

    driver = get_chrome_driver(headless=headless)
    try:
        save_path = download_single_bill(driver, ref_number)
        return {"message": "✅ Completed", "file": f"/static/{save_path.name}"}
    except Exception as e:
        msg = str(e)
        logging.error(f"❌ Failed single download {ref_number}: {msg}")
        logging.debug(traceback.format_exc())
        return JSONResponse(status_code=500, content={"error": msg})
    finally:
        driver.quit()


# --------------------------
# Health check
# --------------------------
@app.get("/api/health")
async def health_check():
    return {"status": "OK", "message": "API is running 🚀"}


# --------------------------
# CLI Mode (debug)
# --------------------------
def run_all_bills_from_file():
    with open(ref_file_path, "r") as f:
        refs = [line.strip() for line in f if line.strip()]
    if not refs:
        print("❌ No reference numbers found")
        return

    for ref in refs:
        attempt = 0
        max_attempts = 2
        while attempt < max_attempts:
            attempt += 1
            driver = None
            try:
                driver = get_chrome_driver(headless=False)
                print(f"⚡ Downloading: {ref} (attempt {attempt})")
                download_single_bill(driver, ref)
                break
            except Exception as e:
                print(f"❌ Failed {ref} on attempt {attempt}: {e}")
                if attempt >= max_attempts:
                    print(f"❌ Giving up on {ref}")
            finally:
                if driver:
                    try:
                        driver.quit()
                    except Exception:
                        pass
    print("🎉 All downloads finished")


# --------------------------
# Main
# --------------------------
if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--server":
        uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
    else:
        run_all_bills_from_file()
