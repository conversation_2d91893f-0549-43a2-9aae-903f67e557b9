{"version": 3, "names": ["elementMap", "Map", "Data", "set", "element", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "keys", "remove", "delete", "TRANSITION_END", "parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "toType", "object", "Object", "prototype", "toString", "call", "toLowerCase", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "getElement", "length", "document", "querySelector", "isVisible", "getClientRects", "elementIsVisible", "getComputedStyle", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "isDisabled", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "noop", "reflow", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "isRTL", "dir", "defineJQueryPlugin", "plugin", "callback", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "readyState", "addEventListener", "push", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "executeAfterTransition", "transitionElement", "waitForTransition", "emulatedDuration", "transitionDuration", "transitionDelay", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "getTransitionDurationFromElement", "called", "handler", "target", "removeEventListener", "setTimeout", "getNextActiveElement", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "Math", "max", "min", "namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "this", "handlers", "previousFunction", "dom<PERSON><PERSON>s", "querySelectorAll", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "slice", "keyHandlers", "trigger", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "obj", "meta", "value", "_unused", "defineProperty", "configurable", "normalizeData", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "chr", "Manipulator", "setDataAttribute", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "dataset", "filter", "pureKey", "char<PERSON>t", "getDataAttribute", "Config", "<PERSON><PERSON><PERSON>", "DefaultType", "Error", "_getConfig", "config", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "jsonConfig", "constructor", "configTypes", "property", "expectedTypes", "valueType", "RegExp", "test", "TypeError", "toUpperCase", "BaseComponent", "super", "_element", "_config", "DATA_KEY", "dispose", "EVENT_KEY", "propertyName", "getOwnPropertyNames", "_queueCallback", "isAnimated", "getInstance", "getOrCreateInstance", "VERSION", "eventName", "getSelector", "hrefAttribute", "trim", "map", "sel", "join", "SelectorEngine", "concat", "Element", "findOne", "children", "child", "matches", "parents", "ancestor", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector", "enableDismissTrigger", "component", "method", "clickEvent", "tagName", "EVENT_CLOSE", "EVENT_CLOSED", "<PERSON><PERSON>", "close", "_destroyElement", "each", "data", "undefined", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON>", "toggle", "button", "EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "Swipe", "isSupported", "_deltaX", "_supportPointerEvents", "PointerEvent", "_initEvents", "_start", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "absDeltaX", "abs", "direction", "add", "pointerType", "navigator", "maxTouchPoints", "DATA_API_KEY", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "ORDER_NEXT", "ORDER_PREV", "DIRECTION_LEFT", "DIRECTION_RIGHT", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "CLASS_NAME_CAROUSEL", "CLASS_NAME_ACTIVE", "SELECTOR_ACTIVE", "SELECTOR_ITEM", "SELECTOR_ACTIVE_ITEM", "KEY_TO_DIRECTION", "ARROW_LEFT_KEY$1", "ARROW_RIGHT_KEY$1", "interval", "keyboard", "pause", "ride", "touch", "wrap", "Carousel", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "_addEventListeners", "cycle", "_slide", "nextWhenVisible", "hidden", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "to", "items", "_getItems", "activeIndex", "_getItemIndex", "_getActive", "order", "defaultInterval", "_keydown", "_addTouchEventListeners", "img", "swipeConfig", "_directionToOrder", "endCallBack", "clearTimeout", "_setActiveIndicatorElement", "activeIndicator", "newActiveIndicator", "elementInterval", "parseInt", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "_orderToDirection", "isCycling", "directionalClassName", "orderClassName", "completeCallBack", "_isAnimated", "clearInterval", "carousel", "slideIndex", "carousels", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_DEEPER_CHILDREN", "parent", "Collapse", "_isTransitioning", "_triggerArray", "toggleList", "elem", "filterElement", "foundElement", "_initializeC<PERSON><PERSON>n", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "activeInstance", "dimension", "_getDimension", "style", "scrollSize", "complete", "getBoundingClientRect", "selected", "trigger<PERSON><PERSON>y", "isOpen", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE_SHOWN", "SELECTOR_MENU", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "Dropdown", "_popper", "_parent", "_menu", "_inNavbar", "_detectNavbar", "_createPopper", "focus", "_completeHide", "destroy", "update", "<PERSON><PERSON>", "referenceElement", "_getPopperConfig", "createPopper", "_getPlacement", "parentDropdown", "isEnd", "_getOffset", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "options", "enabled", "_selectMenuItem", "clearMenus", "openToggles", "context", "<PERSON><PERSON><PERSON>", "isMenuTarget", "dataApiKeydownHandler", "isInput", "isEscapeEvent", "isUpOrDownEvent", "getToggleButton", "stopPropagation", "EVENT_MOUSEDOWN", "className", "clickCallback", "rootElement", "Backdrop", "_isAppended", "_append", "_getElement", "_emulateAnimation", "backdrop", "createElement", "append", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "TAB_NAV_BACKWARD", "autofocus", "trapElement", "FocusTrap", "_isActive", "_lastTabNavDirection", "activate", "_handleFocusin", "_handleKeydown", "deactivate", "elements", "shift<PERSON>ey", "SELECTOR_FIXED_CONTENT", "SELECTOR_STICKY_CONTENT", "PROPERTY_PADDING", "PROPERTY_MARGIN", "ScrollBarHelper", "getWidth", "documentWidth", "clientWidth", "innerWidth", "width", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "overflow", "styleProperty", "scrollbarWidth", "_applyManipulationCallback", "setProperty", "actualValue", "removeProperty", "callBack", "EVENT_HIDE_PREVENTED", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "CLASS_NAME_OPEN", "CLASS_NAME_STATIC", "Modal", "_dialog", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_scrollBar", "_adjustDialog", "_showElement", "_hideModal", "handleUpdate", "scrollTop", "modalBody", "transitionComplete", "_triggerBackdropTransition", "event2", "_resetAdjustments", "isModalOverflowing", "scrollHeight", "clientHeight", "initialOverflowY", "overflowY", "isBodyOverflowing", "paddingLeft", "paddingRight", "showEvent", "alreadyOpen", "CLASS_NAME_SHOWING", "CLASS_NAME_HIDING", "OPEN_SELECTOR", "scroll", "<PERSON><PERSON><PERSON>", "blur", "completeCallback", "position", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "dd", "div", "dl", "dt", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "uriAttributes", "SAFE_URL_PATTERN", "allowedAttribute", "attribute", "allowedAttributeList", "attributeName", "nodeName", "nodeValue", "attributeRegex", "some", "regex", "allowList", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultContentType", "entry", "TemplateFactory", "get<PERSON>ontent", "_resolvePossibleFunction", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "_checkContent", "toHtml", "templateWrapper", "innerHTML", "_maybeSanitize", "text", "_setContent", "arg", "templateElement", "_putElementInTemplate", "textContent", "unsafeHtml", "sanitizeFunction", "createdDocument", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elementName", "attributeList", "allowedAttributes", "sanitizeHtml", "DISALLOWED_ATTRIBUTES", "CLASS_NAME_FADE", "SELECTOR_TOOLTIP_INNER", "SELECTOR_MODAL", "EVENT_MODAL_HIDE", "TRIGGER_HOVER", "TRIGGER_FOCUS", "TRIGGER_CLICK", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "animation", "container", "customClass", "delay", "fallbackPlacements", "title", "<PERSON><PERSON><PERSON>", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_templateFactory", "_newContent", "tip", "_setListeners", "_fixTitle", "enable", "disable", "toggle<PERSON>nabled", "_leave", "_enter", "_hideModalHandler", "_disposePopper", "_isWithContent", "isInTheDom", "ownerDocument", "_getTipElement", "_isWithActiveTrigger", "_getTitle", "_createTipElement", "_getContentForTemplate", "_getTemplateFactory", "tipId", "prefix", "floor", "random", "getElementById", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "_initializeOnDelegatedTarget", "_getDelegateConfig", "attachment", "phase", "state", "triggers", "eventIn", "eventOut", "_setTimeout", "timeout", "dataAttributes", "dataAttribute", "SELECTOR_TITLE", "SELECTOR_CONTENT", "Popover", "_getContent", "EVENT_ACTIVATE", "EVENT_CLICK", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LINKS", "SELECTOR_LINK_ITEMS", "rootMargin", "smoothScroll", "threshold", "ScrollSpy", "_targetLinks", "_observableSections", "_rootElement", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "observe", "observableSection", "hash", "height", "offsetTop", "scrollTo", "top", "behavior", "IntersectionObserver", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "_process", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "anchor", "decodeURI", "_activateParents", "listGroup", "item", "activeNodes", "node", "spy", "HOME_KEY", "END_KEY", "SELECTOR_DROPDOWN_TOGGLE", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "_deactivate", "_activate", "relatedElem", "_toggleDropDown", "nextActiveElement", "preventScroll", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "open", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSOUT", "CLASS_NAME_HIDE", "autohide", "Toast", "_hasMouseInteraction", "_hasKeyboardInteraction", "_clearTimeout", "_maybeScheduleHide", "isShown", "_onInteraction", "isInteracting"], "sources": ["../../js/src/dom/data.js", "../../js/src/util/index.js", "../../js/src/dom/event-handler.js", "../../js/src/dom/manipulator.js", "../../js/src/util/config.js", "../../js/src/base-component.js", "../../js/src/dom/selector-engine.js", "../../js/src/util/component-functions.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/util/swipe.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../js/src/dropdown.js", "../../js/src/util/backdrop.js", "../../js/src/util/focustrap.js", "../../js/src/util/scrollbar.js", "../../js/src/modal.js", "../../js/src/offcanvas.js", "../../js/src/util/sanitizer.js", "../../js/src/util/template-factory.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/toast.js", "../../js/index.umd.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.harrytheo.com/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback.call(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport { isElement, toType } from './index.js'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data.js'\nimport EventHandler from './dom/event-handler.js'\nimport Config from './util/config.js'\nimport { executeAfterTransition, getElement } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.8'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  // Private\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index.js'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isDisabled } from './index.js'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport { execute } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index.js'\nimport Swipe from './util/swipe.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  // Private\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator.js'\nimport SelectorEngine from '../dom/selector-engine.js'\nimport { isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin, isRTL, isVisible, reflow\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport Backdrop from './util/backdrop.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport FocusTrap from './util/focustrap.js'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index.js'\nimport ScrollBarHelper from './util/scrollbar.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine.js'\nimport Config from './config.js'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer.js'\nimport { execute, getElement, isElement } from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [undefined, this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from '@popperjs/core'\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport Manipulator from './dom/manipulator.js'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index.js'\nimport { DefaultAllowlist } from './util/sanitizer.js'\nimport TemplateFactory from './util/template-factory.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org/docs/v2/)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element, this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [undefined, defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[TRIGGER_CLICK] = !(context._isShown() && context._activeTrigger[TRIGGER_CLICK])\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip.js'\nimport { defineJQueryPlugin } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin, getElement, isDisabled, isVisible\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst HOME_KEY = 'Home'\nconst END_KEY = 'End'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n\n    const children = this._getChildren().filter(element => !isDisabled(element))\n    let nextActiveElement\n\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1]\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true)\n    }\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport { enableDismissTrigger } from './util/component-functions.js'\nimport { defineJQueryPlugin, reflow } from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${D<PERSON>A_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n", "/**\n * --------------------------------------------------------------------------\n * Bootstrap index.umd.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport <PERSON><PERSON> from './src/alert.js'\nimport Button from './src/button.js'\nimport Carousel from './src/carousel.js'\nimport Collapse from './src/collapse.js'\nimport Dropdown from './src/dropdown.js'\nimport Modal from './src/modal.js'\nimport Offcanvas from './src/offcanvas.js'\nimport Popover from './src/popover.js'\nimport ScrollSpy from './src/scrollspy.js'\nimport Tab from './src/tab.js'\nimport Toast from './src/toast.js'\nimport Tooltip from './src/tooltip.js'\n\nexport default {\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Offcanvas,\n  Popover,\n  ScrollSpy,\n  Tab,\n  Toast,\n  Tooltip\n}\n"], "mappings": ";;;;;sjBAWMA,EAAa,IAAIC,IAEvBC,EAAe,CACbC,IAAIC,EAASC,EAAKC,GACXN,EAAWO,IAAIH,IAClBJ,EAAWG,IAAIC,EAAS,IAAIH,KAG9B,MAAMO,EAAcR,EAAWS,IAAIL,GAI9BI,EAAYD,IAAIF,IAA6B,IAArBG,EAAYE,KAMzCF,EAAYL,IAAIE,EAAKC,GAJnBK,QAAQC,MAAM,+EAA+EC,MAAMC,KAAKN,EAAYO,QAAQ,MAKhI,EAEAN,IAAGA,CAACL,EAASC,IACPL,EAAWO,IAAIH,IACVJ,EAAWS,IAAIL,GAASK,IAAIJ,IAG9B,KAGTW,OAAOZ,EAASC,GACd,IAAKL,EAAWO,IAAIH,GAClB,OAGF,MAAMI,EAAcR,EAAWS,IAAIL,GAEnCI,EAAYS,OAAOZ,GAGM,IAArBG,EAAYE,MACdV,EAAWiB,OAAOb,EAEtB,GC5CIc,EAAiB,gBAOjBC,EAAgBC,IAChBA,GAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,EAAWA,EAASI,QAAQ,gBAAiB,CAACC,EAAOC,IAAO,IAAIJ,IAAIC,OAAOG,OAGtEN,GAIHO,EAASC,GACTA,QACK,GAAGA,IAGLC,OAAOC,UAAUC,SAASC,KAAKJ,GAAQH,MAAM,eAAe,GAAGQ,cAsClEC,EAAuB9B,IAC3BA,EAAQ+B,cAAc,IAAIC,MAAMlB,KAG5BmB,EAAYT,MACXA,GAA4B,iBAAXA,UAIO,IAAlBA,EAAOU,SAChBV,EAASA,EAAO,SAGgB,IAApBA,EAAOW,UAGjBC,EAAaZ,GAEbS,EAAUT,GACLA,EAAOU,OAASV,EAAO,GAAKA,EAGf,iBAAXA,GAAuBA,EAAOa,OAAS,EACzCC,SAASC,cAAcxB,EAAcS,IAGvC,KAGHgB,EAAYxC,IAChB,IAAKiC,EAAUjC,IAAgD,IAApCA,EAAQyC,iBAAiBJ,OAClD,OAAO,EAGT,MAAMK,EAAgF,YAA7DC,iBAAiB3C,GAAS4C,iBAAiB,cAE9DC,EAAgB7C,EAAQ8C,QAAQ,uBAEtC,IAAKD,EACH,OAAOH,EAGT,GAAIG,IAAkB7C,EAAS,CAC7B,MAAM+C,EAAU/C,EAAQ8C,QAAQ,WAChC,GAAIC,GAAWA,EAAQC,aAAeH,EACpC,OAAO,EAGT,GAAgB,OAAZE,EACF,OAAO,CAEX,CAEA,OAAOL,GAGHO,EAAajD,IACZA,GAAWA,EAAQmC,WAAae,KAAKC,gBAItCnD,EAAQoD,UAAUC,SAAS,mBAIC,IAArBrD,EAAQsD,SACVtD,EAAQsD,SAGVtD,EAAQuD,aAAa,aAAoD,UAArCvD,EAAQwD,aAAa,aAG5DC,EAAiBzD,IACrB,IAAKsC,SAASoB,gBAAgBC,aAC5B,OAAO,KAIT,GAAmC,mBAAxB3D,EAAQ4D,YAA4B,CAC7C,MAAMC,EAAO7D,EAAQ4D,cACrB,OAAOC,aAAgBC,WAAaD,EAAO,IAC7C,CAEA,OAAI7D,aAAmB8D,WACd9D,EAIJA,EAAQgD,WAINS,EAAezD,EAAQgD,YAHrB,MAMLe,EAAOA,OAUPC,EAAShE,IACbA,EAAQiE,cAGJC,EAAYA,IACZjD,OAAOkD,SAAW7B,SAAS8B,KAAKb,aAAa,qBACxCtC,OAAOkD,OAGT,KAGHE,EAA4B,GAmB5BC,EAAQA,IAAuC,QAAjChC,SAASoB,gBAAgBa,IAEvCC,EAAqBC,IAnBAC,QAoBN,KACjB,MAAMC,EAAIT,IAEV,GAAIS,EAAG,CACL,MAAMC,EAAOH,EAAOI,KACdC,EAAqBH,EAAEI,GAAGH,GAChCD,EAAEI,GAAGH,GAAQH,EAAOO,gBACpBL,EAAEI,GAAGH,GAAMK,YAAcR,EACzBE,EAAEI,GAAGH,GAAMM,WAAa,KACtBP,EAAEI,GAAGH,GAAQE,EACNL,EAAOO,gBAElB,GA/B0B,YAAxB1C,SAAS6C,YAENd,EAA0BhC,QAC7BC,SAAS8C,iBAAiB,mBAAoB,KAC5C,IAAK,MAAMV,KAAYL,EACrBK,MAKNL,EAA0BgB,KAAKX,IAE/BA,KAuBEY,EAAUA,CAACC,EAAkBC,EAAO,GAAIC,EAAeF,IACxB,mBAArBA,EAAkCA,EAAiB3D,QAAQ4D,GAAQC,EAG7EC,EAAyBA,CAAChB,EAAUiB,EAAmBC,GAAoB,KAC/E,IAAKA,EAEH,YADAN,EAAQZ,GAIV,MACMmB,EA7LiC7F,KACvC,IAAKA,EACH,OAAO,EAIT,IAAI8F,mBAAEA,EAAkBC,gBAAEA,GAAoB9E,OAAO0B,iBAAiB3C,GAEtE,MAAMgG,EAA0BC,OAAOC,WAAWJ,GAC5CK,EAAuBF,OAAOC,WAAWH,GAG/C,OAAKC,GAA4BG,GAKjCL,EAAqBA,EAAmBM,MAAM,KAAK,GACnDL,EAAkBA,EAAgBK,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWJ,GAAsBG,OAAOC,WAAWH,KAPzD,GAgLgBM,CAAiCV,GADlC,EAGxB,IAAIW,GAAS,EAEb,MAAMC,EAAUA,EAAGC,aACbA,IAAWb,IAIfW,GAAS,EACTX,EAAkBc,oBAAoB3F,EAAgByF,GACtDjB,EAAQZ,KAGViB,EAAkBP,iBAAiBtE,EAAgByF,GACnDG,WAAW,KACJJ,GACHxE,EAAqB6D,IAEtBE,IAYCc,EAAuBA,CAACC,EAAMC,EAAeC,EAAeC,KAChE,MAAMC,EAAaJ,EAAKvE,OACxB,IAAI4E,EAAQL,EAAKM,QAAQL,GAIzB,OAAc,IAAVI,GACMH,GAAiBC,EAAiBH,EAAKI,EAAa,GAAKJ,EAAK,IAGxEK,GAASH,EAAgB,GAAI,EAEzBC,IACFE,GAASA,EAAQD,GAAcA,GAG1BJ,EAAKO,KAAKC,IAAI,EAAGD,KAAKE,IAAIJ,EAAOD,EAAa,OC7QjDM,EAAiB,qBACjBC,EAAiB,OACjBC,EAAgB,SAChBC,EAAgB,GACtB,IAAIC,EAAW,EACf,MAAMC,EAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,EAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,WAOF,SAASC,EAAahI,EAASiI,GAC7B,OAAQA,GAAO,GAAGA,MAAQP,OAAiB1H,EAAQ0H,UAAYA,GACjE,CAEA,SAASQ,EAAiBlI,GACxB,MAAMiI,EAAMD,EAAahI,GAKzB,OAHAA,EAAQ0H,SAAWO,EACnBR,EAAcQ,GAAOR,EAAcQ,IAAQ,GAEpCR,EAAcQ,EACvB,CAoCA,SAASE,EAAYC,EAAQC,EAAUC,EAAqB,MAC1D,OAAO7G,OAAO8G,OAAOH,GAClBI,KAAKC,GAASA,EAAMJ,WAAaA,GAAYI,EAAMH,qBAAuBA,EAC/E,CAEA,SAASI,EAAoBC,EAAmBpC,EAASqC,GACvD,MAAMC,EAAiC,iBAAZtC,EAErB8B,EAAWQ,EAAcD,EAAsBrC,GAAWqC,EAChE,IAAIE,EAAYC,EAAaJ,GAM7B,OAJKb,EAAa3H,IAAI2I,KACpBA,EAAYH,GAGP,CAACE,EAAaR,EAAUS,EACjC,CAEA,SAASE,EAAWhJ,EAAS2I,EAAmBpC,EAASqC,EAAoBK,GAC3E,GAAiC,iBAAtBN,IAAmC3I,EAC5C,OAGF,IAAK6I,EAAaR,EAAUS,GAAaJ,EAAoBC,EAAmBpC,EAASqC,GAIzF,GAAID,KAAqBhB,EAAc,CACrC,MAAMuB,EAAenE,GACZ,SAAU0D,GACf,IAAKA,EAAMU,eAAkBV,EAAMU,gBAAkBV,EAAMW,iBAAmBX,EAAMW,eAAe/F,SAASoF,EAAMU,eAChH,OAAOpE,EAAGnD,KAAKyH,KAAMZ,EAEzB,EAGFJ,EAAWa,EAAab,EAC1B,CAEA,MAAMD,EAASF,EAAiBlI,GAC1BsJ,EAAWlB,EAAOU,KAAeV,EAAOU,GAAa,IACrDS,EAAmBpB,EAAYmB,EAAUjB,EAAUQ,EAActC,EAAU,MAEjF,GAAIgD,EAGF,YAFAA,EAAiBN,OAASM,EAAiBN,QAAUA,GAKvD,MAAMhB,EAAMD,EAAaK,EAAUM,EAAkBvH,QAAQkG,EAAgB,KACvEvC,EAAK8D,EAxEb,SAAoC7I,EAASgB,EAAU+D,GACrD,OAAO,SAASwB,EAAQkC,GACtB,MAAMe,EAAcxJ,EAAQyJ,iBAAiBzI,GAE7C,IAAK,IAAIwF,OAAEA,GAAWiC,EAAOjC,GAAUA,IAAW6C,KAAM7C,EAASA,EAAOxD,WACtE,IAAK,MAAM0G,KAAcF,EACvB,GAAIE,IAAelD,EAUnB,OANAmD,EAAWlB,EAAO,CAAEW,eAAgB5C,IAEhCD,EAAQ0C,QACVW,EAAaC,IAAI7J,EAASyI,EAAMqB,KAAM9I,EAAU+D,GAG3CA,EAAGgF,MAAMvD,EAAQ,CAACiC,GAG/B,CACF,CAqDIuB,CAA2BhK,EAASuG,EAAS8B,GArFjD,SAA0BrI,EAAS+E,GACjC,OAAO,SAASwB,EAAQkC,GAOtB,OANAkB,EAAWlB,EAAO,CAAEW,eAAgBpJ,IAEhCuG,EAAQ0C,QACVW,EAAaC,IAAI7J,EAASyI,EAAMqB,KAAM/E,GAGjCA,EAAGgF,MAAM/J,EAAS,CAACyI,GAC5B,CACF,CA4EIwB,CAAiBjK,EAASqI,GAE5BtD,EAAGuD,mBAAqBO,EAActC,EAAU,KAChDxB,EAAGsD,SAAWA,EACdtD,EAAGkE,OAASA,EACZlE,EAAG2C,SAAWO,EACdqB,EAASrB,GAAOlD,EAEhB/E,EAAQoF,iBAAiB0D,EAAW/D,EAAI8D,EAC1C,CAEA,SAASqB,EAAclK,EAASoI,EAAQU,EAAWvC,EAAS+B,GAC1D,MAAMvD,EAAKoD,EAAYC,EAAOU,GAAYvC,EAAS+B,GAE9CvD,IAIL/E,EAAQyG,oBAAoBqC,EAAW/D,EAAIoF,QAAQ7B,WAC5CF,EAAOU,GAAW/D,EAAG2C,UAC9B,CAEA,SAAS0C,EAAyBpK,EAASoI,EAAQU,EAAWuB,GAC5D,MAAMC,EAAoBlC,EAAOU,IAAc,GAE/C,IAAK,MAAOyB,EAAY9B,KAAUhH,OAAO+I,QAAQF,GAC3CC,EAAWE,SAASJ,IACtBH,EAAclK,EAASoI,EAAQU,EAAWL,EAAMJ,SAAUI,EAAMH,mBAGtE,CAEA,SAASS,EAAaN,GAGpB,OADAA,EAAQA,EAAMrH,QAAQmG,EAAgB,IAC/BI,EAAac,IAAUA,CAChC,CAEA,MAAMmB,EAAe,CACnBc,GAAG1K,EAASyI,EAAOlC,EAASqC,GAC1BI,EAAWhJ,EAASyI,EAAOlC,EAASqC,GAAoB,EAC1D,EAEA+B,IAAI3K,EAASyI,EAAOlC,EAASqC,GAC3BI,EAAWhJ,EAASyI,EAAOlC,EAASqC,GAAoB,EAC1D,EAEAiB,IAAI7J,EAAS2I,EAAmBpC,EAASqC,GACvC,GAAiC,iBAAtBD,IAAmC3I,EAC5C,OAGF,MAAO6I,EAAaR,EAAUS,GAAaJ,EAAoBC,EAAmBpC,EAASqC,GACrFgC,EAAc9B,IAAcH,EAC5BP,EAASF,EAAiBlI,GAC1BsK,EAAoBlC,EAAOU,IAAc,GACzC+B,EAAclC,EAAkBmC,WAAW,KAEjD,QAAwB,IAAbzC,EAAX,CAUA,GAAIwC,EACF,IAAK,MAAME,KAAgBtJ,OAAOd,KAAKyH,GACrCgC,EAAyBpK,EAASoI,EAAQ2C,EAAcpC,EAAkBqC,MAAM,IAIpF,IAAK,MAAOC,EAAaxC,KAAUhH,OAAO+I,QAAQF,GAAoB,CACpE,MAAMC,EAAaU,EAAY7J,QAAQoG,EAAe,IAEjDoD,IAAejC,EAAkB8B,SAASF,IAC7CL,EAAclK,EAASoI,EAAQU,EAAWL,EAAMJ,SAAUI,EAAMH,mBAEpE,CAdA,KARA,CAEE,IAAK7G,OAAOd,KAAK2J,GAAmBjI,OAClC,OAGF6H,EAAclK,EAASoI,EAAQU,EAAWT,EAAUQ,EAActC,EAAU,KAE9E,CAeF,EAEA2E,QAAQlL,EAASyI,EAAOjD,GACtB,GAAqB,iBAAViD,IAAuBzI,EAChC,OAAO,KAGT,MAAM2E,EAAIT,IAIV,IAAIiH,EAAc,KACdC,GAAU,EACVC,GAAiB,EACjBC,GAAmB,EALH7C,IADFM,EAAaN,IAQZ9D,IACjBwG,EAAcxG,EAAE3C,MAAMyG,EAAOjD,GAE7Bb,EAAE3E,GAASkL,QAAQC,GACnBC,GAAWD,EAAYI,uBACvBF,GAAkBF,EAAYK,gCAC9BF,EAAmBH,EAAYM,sBAGjC,MAAMC,EAAM/B,EAAW,IAAI3H,MAAMyG,EAAO,CAAE2C,UAASO,YAAY,IAASnG,GAcxE,OAZI8F,GACFI,EAAIE,iBAGFP,GACFrL,EAAQ+B,cAAc2J,GAGpBA,EAAIJ,kBAAoBH,GAC1BA,EAAYS,iBAGPF,CACT,GAGF,SAAS/B,EAAWkC,EAAKC,EAAO,IAC9B,IAAK,MAAO7L,EAAK8L,KAAUtK,OAAO+I,QAAQsB,GACxC,IACED,EAAI5L,GAAO8L,CACb,CAAE,MAAAC,GACAvK,OAAOwK,eAAeJ,EAAK5L,EAAK,CAC9BiM,cAAc,EACd7L,IAAGA,IACM0L,GAGb,CAGF,OAAOF,CACT,CCnTA,SAASM,EAAcJ,GACrB,GAAc,SAAVA,EACF,OAAO,EAGT,GAAc,UAAVA,EACF,OAAO,EAGT,GAAIA,IAAU9F,OAAO8F,GAAOpK,WAC1B,OAAOsE,OAAO8F,GAGhB,GAAc,KAAVA,GAA0B,SAAVA,EAClB,OAAO,KAGT,GAAqB,iBAAVA,EACT,OAAOA,EAGT,IACE,OAAOK,KAAKC,MAAMC,mBAAmBP,GACvC,CAAE,MAAAC,GACA,OAAOD,CACT,CACF,CAEA,SAASQ,EAAiBtM,GACxB,OAAOA,EAAImB,QAAQ,SAAUoL,GAAO,IAAIA,EAAI3K,gBAC9C,CAEA,MAAM4K,EAAc,CAClBC,iBAAiB1M,EAASC,EAAK8L,GAC7B/L,EAAQ2M,aAAa,WAAWJ,EAAiBtM,KAAQ8L,EAC3D,EAEAa,oBAAoB5M,EAASC,GAC3BD,EAAQ6M,gBAAgB,WAAWN,EAAiBtM,KACtD,EAEA6M,kBAAkB9M,GAChB,IAAKA,EACH,MAAO,GAGT,MAAM+M,EAAa,GACbC,EAASvL,OAAOd,KAAKX,EAAQiN,SAASC,OAAOjN,GAAOA,EAAI6K,WAAW,QAAU7K,EAAI6K,WAAW,aAElG,IAAK,MAAM7K,KAAO+M,EAAQ,CACxB,IAAIG,EAAUlN,EAAImB,QAAQ,MAAO,IACjC+L,EAAUA,EAAQC,OAAO,GAAGvL,cAAgBsL,EAAQnC,MAAM,GAC1D+B,EAAWI,GAAWhB,EAAcnM,EAAQiN,QAAQhN,GACtD,CAEA,OAAO8M,CACT,EAEAM,iBAAgBA,CAACrN,EAASC,IACjBkM,EAAcnM,EAAQwD,aAAa,WAAW+I,EAAiBtM,QCpD1E,MAAMqN,EAEJ,kBAAWC,GACT,MAAO,EACT,CAEA,sBAAWC,GACT,MAAO,EACT,CAEA,eAAW3I,GACT,MAAM,IAAI4I,MAAM,sEAClB,CAEAC,WAAWC,GAIT,OAHAA,EAAStE,KAAKuE,gBAAgBD,GAC9BA,EAAStE,KAAKwE,kBAAkBF,GAChCtE,KAAKyE,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAChB,OAAOA,CACT,CAEAC,gBAAgBD,EAAQ3N,GACtB,MAAM+N,EAAa9L,EAAUjC,GAAWyM,EAAYY,iBAAiBrN,EAAS,UAAY,GAE1F,MAAO,IACFqJ,KAAK2E,YAAYT,WACM,iBAAfQ,EAA0BA,EAAa,MAC9C9L,EAAUjC,GAAWyM,EAAYK,kBAAkB9M,GAAW,MAC5C,iBAAX2N,EAAsBA,EAAS,GAE9C,CAEAG,iBAAiBH,EAAQM,EAAc5E,KAAK2E,YAAYR,aACtD,IAAK,MAAOU,EAAUC,KAAkB1M,OAAO+I,QAAQyD,GAAc,CACnE,MAAMlC,EAAQ4B,EAAOO,GACfE,EAAYnM,EAAU8J,GAAS,UAAYxK,EAAOwK,GAExD,IAAK,IAAIsC,OAAOF,GAAeG,KAAKF,GAClC,MAAM,IAAIG,UACR,GAAGlF,KAAK2E,YAAYnJ,KAAK2J,0BAA0BN,qBAA4BE,yBAAiCD,MAGtH,CACF,ECvCF,MAAMM,UAAsBnB,EAC1BU,YAAYhO,EAAS2N,GACnBe,SAEA1O,EAAUoC,EAAWpC,MAKrBqJ,KAAKsF,SAAW3O,EAChBqJ,KAAKuF,QAAUvF,KAAKqE,WAAWC,GAE/B7N,EAAKC,IAAIsJ,KAAKsF,SAAUtF,KAAK2E,YAAYa,SAAUxF,MACrD,CAGAyF,UACEhP,EAAKc,OAAOyI,KAAKsF,SAAUtF,KAAK2E,YAAYa,UAC5CjF,EAAaC,IAAIR,KAAKsF,SAAUtF,KAAK2E,YAAYe,WAEjD,IAAK,MAAMC,KAAgBvN,OAAOwN,oBAAoB5F,MACpDA,KAAK2F,GAAgB,IAEzB,CAGAE,eAAexK,EAAU1E,EAASmP,GAAa,GAC7CzJ,EAAuBhB,EAAU1E,EAASmP,EAC5C,CAEAzB,WAAWC,GAIT,OAHAA,EAAStE,KAAKuE,gBAAgBD,EAAQtE,KAAKsF,UAC3ChB,EAAStE,KAAKwE,kBAAkBF,GAChCtE,KAAKyE,iBAAiBH,GACfA,CACT,CAGA,kBAAOyB,CAAYpP,GACjB,OAAOF,EAAKO,IAAI+B,EAAWpC,GAAUqJ,KAAKwF,SAC5C,CAEA,0BAAOQ,CAAoBrP,EAAS2N,EAAS,IAC3C,OAAOtE,KAAK+F,YAAYpP,IAAY,IAAIqJ,KAAKrJ,EAA2B,iBAAX2N,EAAsBA,EAAS,KAC9F,CAEA,kBAAW2B,GACT,MArDY,OAsDd,CAEA,mBAAWT,GACT,MAAO,MAAMxF,KAAKxE,MACpB,CAEA,oBAAWkK,GACT,MAAO,IAAI1F,KAAKwF,UAClB,CAEA,gBAAOU,CAAU3K,GACf,MAAO,GAAGA,IAAOyE,KAAK0F,WACxB,ECzEF,MAAMS,EAAcxP,IAClB,IAAIgB,EAAWhB,EAAQwD,aAAa,kBAEpC,IAAKxC,GAAyB,MAAbA,EAAkB,CACjC,IAAIyO,EAAgBzP,EAAQwD,aAAa,QAMzC,IAAKiM,IAAmBA,EAAchF,SAAS,OAASgF,EAAc3E,WAAW,KAC/E,OAAO,KAIL2E,EAAchF,SAAS,OAASgF,EAAc3E,WAAW,OAC3D2E,EAAgB,IAAIA,EAAcrJ,MAAM,KAAK,MAG/CpF,EAAWyO,GAAmC,MAAlBA,EAAwBA,EAAcC,OAAS,IAC7E,CAEA,OAAO1O,EAAWA,EAASoF,MAAM,KAAKuJ,IAAIC,GAAO7O,EAAc6O,IAAMC,KAAK,KAAO,MAG7EC,EAAiB,CACrBtH,KAAIA,CAACxH,EAAUhB,EAAUsC,SAASoB,kBACzB,GAAGqM,UAAUC,QAAQtO,UAAU+H,iBAAiB7H,KAAK5B,EAASgB,IAGvEiP,QAAOA,CAACjP,EAAUhB,EAAUsC,SAASoB,kBAC5BsM,QAAQtO,UAAUa,cAAcX,KAAK5B,EAASgB,GAGvDkP,SAAQA,CAAClQ,EAASgB,IACT,GAAG+O,UAAU/P,EAAQkQ,UAAUhD,OAAOiD,GAASA,EAAMC,QAAQpP,IAGtEqP,QAAQrQ,EAASgB,GACf,MAAMqP,EAAU,GAChB,IAAIC,EAAWtQ,EAAQgD,WAAWF,QAAQ9B,GAE1C,KAAOsP,GACLD,EAAQhL,KAAKiL,GACbA,EAAWA,EAAStN,WAAWF,QAAQ9B,GAGzC,OAAOqP,CACT,EAEAE,KAAKvQ,EAASgB,GACZ,IAAIwP,EAAWxQ,EAAQyQ,uBAEvB,KAAOD,GAAU,CACf,GAAIA,EAASJ,QAAQpP,GACnB,MAAO,CAACwP,GAGVA,EAAWA,EAASC,sBACtB,CAEA,MAAO,EACT,EAEAC,KAAK1Q,EAASgB,GACZ,IAAI0P,EAAO1Q,EAAQ2Q,mBAEnB,KAAOD,GAAM,CACX,GAAIA,EAAKN,QAAQpP,GACf,MAAO,CAAC0P,GAGVA,EAAOA,EAAKC,kBACd,CAEA,MAAO,EACT,EAEAC,kBAAkB5Q,GAChB,MAAM6Q,EAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACAlB,IAAI3O,GAAY,GAAGA,0BAAiC6O,KAAK,KAE3D,OAAOxG,KAAKb,KAAKqI,EAAY7Q,GAASkN,OAAO4D,IAAO7N,EAAW6N,IAAOtO,EAAUsO,GAClF,EAEAC,uBAAuB/Q,GACrB,MAAMgB,EAAWwO,EAAYxP,GAE7B,OAAIgB,GACK8O,EAAeG,QAAQjP,GAAYA,EAGrC,IACT,EAEAgQ,uBAAuBhR,GACrB,MAAMgB,EAAWwO,EAAYxP,GAE7B,OAAOgB,EAAW8O,EAAeG,QAAQjP,GAAY,IACvD,EAEAiQ,gCAAgCjR,GAC9B,MAAMgB,EAAWwO,EAAYxP,GAE7B,OAAOgB,EAAW8O,EAAetH,KAAKxH,GAAY,EACpD,GC/GIkQ,EAAuBA,CAACC,EAAWC,EAAS,UAChD,MAAMC,EAAa,gBAAgBF,EAAUpC,YACvCnK,EAAOuM,EAAUtM,KAEvB+E,EAAac,GAAGpI,SAAU+O,EAAY,qBAAqBzM,MAAU,SAAU6D,GAK7E,GAJI,CAAC,IAAK,QAAQgC,SAASpB,KAAKiI,UAC9B7I,EAAMmD,iBAGJ3I,EAAWoG,MACb,OAGF,MAAM7C,EAASsJ,EAAekB,uBAAuB3H,OAASA,KAAKvG,QAAQ,IAAI8B,KAC9DuM,EAAU9B,oBAAoB7I,GAGtC4K,IACX,ICXIrC,EAAY,YAEZwC,EAAc,QAAQxC,IACtByC,EAAe,SAASzC,IAQ9B,MAAM0C,UAAchD,EAElB,eAAW5J,GACT,MAhBS,OAiBX,CAGA6M,QAGE,GAFmB9H,EAAasB,QAAQ7B,KAAKsF,SAAU4C,GAExCjG,iBACb,OAGFjC,KAAKsF,SAASvL,UAAUxC,OApBJ,QAsBpB,MAAMuO,EAAa9F,KAAKsF,SAASvL,UAAUC,SAvBvB,QAwBpBgG,KAAK6F,eAAe,IAAM7F,KAAKsI,kBAAmBtI,KAAKsF,SAAUQ,EACnE,CAGAwC,kBACEtI,KAAKsF,SAAS/N,SACdgJ,EAAasB,QAAQ7B,KAAKsF,SAAU6C,GACpCnI,KAAKyF,SACP,CAGA,sBAAO9J,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOJ,EAAMpC,oBAAoBhG,MAEvC,GAAsB,iBAAXsE,EAAX,CAIA,QAAqBmE,IAAjBD,EAAKlE,IAAyBA,EAAO7C,WAAW,MAAmB,gBAAX6C,EAC1D,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,GAAQtE,KANb,CAOF,EACF,EAOF6H,EAAqBO,EAAO,SAM5BjN,EAAmBiN,GCrEnB,MAMMM,EAAuB,4BAO7B,MAAMC,UAAevD,EAEnB,eAAW5J,GACT,MAhBS,QAiBX,CAGAoN,SAEE5I,KAAKsF,SAAShC,aAAa,eAAgBtD,KAAKsF,SAASvL,UAAU6O,OAjB7C,UAkBxB,CAGA,sBAAOjN,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOG,EAAO3C,oBAAoBhG,MAEzB,WAAXsE,GACFkE,EAAKlE,IAET,EACF,EAOF/D,EAAac,GAAGpI,SAlCa,2BAkCmByP,EAAsBtJ,IACpEA,EAAMmD,iBAEN,MAAMsG,EAASzJ,EAAMjC,OAAO1D,QAAQiP,GACvBC,EAAO3C,oBAAoB6C,GAEnCD,WAOPzN,EAAmBwN,GCtDnB,MACMjD,GAAY,YACZoD,GAAmB,aAAapD,KAChCqD,GAAkB,YAAYrD,KAC9BsD,GAAiB,WAAWtD,KAC5BuD,GAAoB,cAAcvD,KAClCwD,GAAkB,YAAYxD,KAM9BxB,GAAU,CACdiF,YAAa,KACbC,aAAc,KACdC,cAAe,MAGXlF,GAAc,CAClBgF,YAAa,kBACbC,aAAc,kBACdC,cAAe,mBAOjB,MAAMC,WAAcrF,EAClBU,YAAYhO,EAAS2N,GACnBe,QACArF,KAAKsF,SAAW3O,EAEXA,GAAY2S,GAAMC,gBAIvBvJ,KAAKuF,QAAUvF,KAAKqE,WAAWC,GAC/BtE,KAAKwJ,QAAU,EACfxJ,KAAKyJ,sBAAwB3I,QAAQlJ,OAAO8R,cAC5C1J,KAAK2J,cACP,CAGA,kBAAWzF,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MArDS,OAsDX,CAGAiK,UACElF,EAAaC,IAAIR,KAAKsF,SAAUI,GAClC,CAGAkE,OAAOxK,GACAY,KAAKyJ,sBAMNzJ,KAAK6J,wBAAwBzK,KAC/BY,KAAKwJ,QAAUpK,EAAM0K,SANrB9J,KAAKwJ,QAAUpK,EAAM2K,QAAQ,GAAGD,OAQpC,CAEAE,KAAK5K,GACCY,KAAK6J,wBAAwBzK,KAC/BY,KAAKwJ,QAAUpK,EAAM0K,QAAU9J,KAAKwJ,SAGtCxJ,KAAKiK,eACLhO,EAAQ+D,KAAKuF,QAAQ4D,YACvB,CAEAe,MAAM9K,GACJY,KAAKwJ,QAAUpK,EAAM2K,SAAW3K,EAAM2K,QAAQ/Q,OAAS,EACrD,EACAoG,EAAM2K,QAAQ,GAAGD,QAAU9J,KAAKwJ,OACpC,CAEAS,eACE,MAAME,EAAYrM,KAAKsM,IAAIpK,KAAKwJ,SAEhC,GAAIW,GAlFgB,GAmFlB,OAGF,MAAME,EAAYF,EAAYnK,KAAKwJ,QAEnCxJ,KAAKwJ,QAAU,EAEVa,GAILpO,EAAQoO,EAAY,EAAIrK,KAAKuF,QAAQ8D,cAAgBrJ,KAAKuF,QAAQ6D,aACpE,CAEAO,cACM3J,KAAKyJ,uBACPlJ,EAAac,GAAGrB,KAAKsF,SAAU2D,GAAmB7J,GAASY,KAAK4J,OAAOxK,IACvEmB,EAAac,GAAGrB,KAAKsF,SAAU4D,GAAiB9J,GAASY,KAAKgK,KAAK5K,IAEnEY,KAAKsF,SAASvL,UAAUuQ,IAvGG,mBAyG3B/J,EAAac,GAAGrB,KAAKsF,SAAUwD,GAAkB1J,GAASY,KAAK4J,OAAOxK,IACtEmB,EAAac,GAAGrB,KAAKsF,SAAUyD,GAAiB3J,GAASY,KAAKkK,MAAM9K,IACpEmB,EAAac,GAAGrB,KAAKsF,SAAU0D,GAAgB5J,GAASY,KAAKgK,KAAK5K,IAEtE,CAEAyK,wBAAwBzK,GACtB,OAAOY,KAAKyJ,wBAjHS,QAiHiBrK,EAAMmL,aAlHrB,UAkHyDnL,EAAMmL,YACxF,CAGA,kBAAOhB,GACL,MAAO,iBAAkBtQ,SAASoB,iBAAmBmQ,UAAUC,eAAiB,CAClF,ECrHF,MAEM/E,GAAY,eACZgF,GAAe,YAEfC,GAAiB,YACjBC,GAAkB,aAGlBC,GAAa,OACbC,GAAa,OACbC,GAAiB,OACjBC,GAAkB,QAElBC,GAAc,QAAQvF,KACtBwF,GAAa,OAAOxF,KACpByF,GAAgB,UAAUzF,KAC1B0F,GAAmB,aAAa1F,KAChC2F,GAAmB,aAAa3F,KAChC4F,GAAmB,YAAY5F,KAC/B6F,GAAsB,OAAO7F,KAAYgF,KACzCc,GAAuB,QAAQ9F,KAAYgF,KAE3Ce,GAAsB,WACtBC,GAAoB,SAOpBC,GAAkB,UAClBC,GAAgB,iBAChBC,GAAuBF,GAAkBC,GAMzCE,GAAmB,CACvBC,CAACpB,IAAiBK,GAClBgB,CAACpB,IAAkBG,IAGf7G,GAAU,CACd+H,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGFnI,GAAc,CAClB8H,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,WAOR,MAAMC,WAAiBnH,EACrBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAEftE,KAAKwM,UAAY,KACjBxM,KAAKyM,eAAiB,KACtBzM,KAAK0M,YAAa,EAClB1M,KAAK2M,aAAe,KACpB3M,KAAK4M,aAAe,KAEpB5M,KAAK6M,mBAAqBpG,EAAeG,QAzCjB,uBAyC8C5G,KAAKsF,UAC3EtF,KAAK8M,qBAED9M,KAAKuF,QAAQ6G,OAASX,IACxBzL,KAAK+M,OAET,CAGA,kBAAW7I,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MA9FS,UA+FX,CAGA6L,OACErH,KAAKgN,OAAOnC,GACd,CAEAoC,mBAIOhU,SAASiU,QAAU/T,EAAU6G,KAAKsF,WACrCtF,KAAKqH,MAET,CAEAH,OACElH,KAAKgN,OAAOlC,GACd,CAEAqB,QACMnM,KAAK0M,YACPjU,EAAqBuH,KAAKsF,UAG5BtF,KAAKmN,gBACP,CAEAJ,QACE/M,KAAKmN,iBACLnN,KAAKoN,kBAELpN,KAAKwM,UAAYa,YAAY,IAAMrN,KAAKiN,kBAAmBjN,KAAKuF,QAAQ0G,SAC1E,CAEAqB,oBACOtN,KAAKuF,QAAQ6G,OAIdpM,KAAK0M,WACPnM,EAAae,IAAItB,KAAKsF,SAAU4F,GAAY,IAAMlL,KAAK+M,SAIzD/M,KAAK+M,QACP,CAEAQ,GAAG3P,GACD,MAAM4P,EAAQxN,KAAKyN,YACnB,GAAI7P,EAAQ4P,EAAMxU,OAAS,GAAK4E,EAAQ,EACtC,OAGF,GAAIoC,KAAK0M,WAEP,YADAnM,EAAae,IAAItB,KAAKsF,SAAU4F,GAAY,IAAMlL,KAAKuN,GAAG3P,IAI5D,MAAM8P,EAAc1N,KAAK2N,cAAc3N,KAAK4N,cAC5C,GAAIF,IAAgB9P,EAClB,OAGF,MAAMiQ,EAAQjQ,EAAQ8P,EAAc7C,GAAaC,GAEjD9K,KAAKgN,OAAOa,EAAOL,EAAM5P,GAC3B,CAEA6H,UACMzF,KAAK4M,cACP5M,KAAK4M,aAAanH,UAGpBJ,MAAMI,SACR,CAGAjB,kBAAkBF,GAEhB,OADAA,EAAOwJ,gBAAkBxJ,EAAO2H,SACzB3H,CACT,CAEAwI,qBACM9M,KAAKuF,QAAQ2G,UACf3L,EAAac,GAAGrB,KAAKsF,SAAU6F,GAAe/L,GAASY,KAAK+N,SAAS3O,IAG5C,UAAvBY,KAAKuF,QAAQ4G,QACf5L,EAAac,GAAGrB,KAAKsF,SAAU8F,GAAkB,IAAMpL,KAAKmM,SAC5D5L,EAAac,GAAGrB,KAAKsF,SAAU+F,GAAkB,IAAMrL,KAAKsN,sBAG1DtN,KAAKuF,QAAQ8G,OAAS/C,GAAMC,eAC9BvJ,KAAKgO,yBAET,CAEAA,0BACE,IAAK,MAAMC,KAAOxH,EAAetH,KAhKX,qBAgKmCa,KAAKsF,UAC5D/E,EAAac,GAAG4M,EAAK3C,GAAkBlM,GAASA,EAAMmD,kBAGxD,MAqBM2L,EAAc,CAClB9E,aAAcA,IAAMpJ,KAAKgN,OAAOhN,KAAKmO,kBAAkBpD,KACvD1B,cAAeA,IAAMrJ,KAAKgN,OAAOhN,KAAKmO,kBAAkBnD,KACxD7B,YAxBkBiF,KACS,UAAvBpO,KAAKuF,QAAQ4G,QAYjBnM,KAAKmM,QACDnM,KAAK2M,cACP0B,aAAarO,KAAK2M,cAGpB3M,KAAK2M,aAAetP,WAAW,IAAM2C,KAAKsN,oBAjNjB,IAiN+DtN,KAAKuF,QAAQ0G,aASvGjM,KAAK4M,aAAe,IAAItD,GAAMtJ,KAAKsF,SAAU4I,EAC/C,CAEAH,SAAS3O,GACP,GAAI,kBAAkB6F,KAAK7F,EAAMjC,OAAO8K,SACtC,OAGF,MAAMoC,EAAYyB,GAAiB1M,EAAMxI,KACrCyT,IACFjL,EAAMmD,iBACNvC,KAAKgN,OAAOhN,KAAKmO,kBAAkB9D,IAEvC,CAEAsD,cAAchX,GACZ,OAAOqJ,KAAKyN,YAAY5P,QAAQlH,EAClC,CAEA2X,2BAA2B1Q,GACzB,IAAKoC,KAAK6M,mBACR,OAGF,MAAM0B,EAAkB9H,EAAeG,QAAQ+E,GAAiB3L,KAAK6M,oBAErE0B,EAAgBxU,UAAUxC,OAAOmU,IACjC6C,EAAgB/K,gBAAgB,gBAEhC,MAAMgL,EAAqB/H,EAAeG,QAAQ,sBAAsBhJ,MAAWoC,KAAK6M,oBAEpF2B,IACFA,EAAmBzU,UAAUuQ,IAAIoB,IACjC8C,EAAmBlL,aAAa,eAAgB,QAEpD,CAEA8J,kBACE,MAAMzW,EAAUqJ,KAAKyM,gBAAkBzM,KAAK4N,aAE5C,IAAKjX,EACH,OAGF,MAAM8X,EAAkB7R,OAAO8R,SAAS/X,EAAQwD,aAAa,oBAAqB,IAElF6F,KAAKuF,QAAQ0G,SAAWwC,GAAmBzO,KAAKuF,QAAQuI,eAC1D,CAEAd,OAAOa,EAAOlX,EAAU,MACtB,GAAIqJ,KAAK0M,WACP,OAGF,MAAMlP,EAAgBwC,KAAK4N,aACrBe,EAASd,IAAUhD,GACnB+D,EAAcjY,GAAW2G,EAAqB0C,KAAKyN,YAAajQ,EAAemR,EAAQ3O,KAAKuF,QAAQ+G,MAE1G,GAAIsC,IAAgBpR,EAClB,OAGF,MAAMqR,EAAmB7O,KAAK2N,cAAciB,GAEtCE,EAAe5I,GACZ3F,EAAasB,QAAQ7B,KAAKsF,SAAUY,EAAW,CACpDpG,cAAe8O,EACfvE,UAAWrK,KAAK+O,kBAAkBlB,GAClCxW,KAAM2I,KAAK2N,cAAcnQ,GACzB+P,GAAIsB,IAMR,GAFmBC,EAAa7D,IAEjBhJ,iBACb,OAGF,IAAKzE,IAAkBoR,EAGrB,OAGF,MAAMI,EAAYlO,QAAQd,KAAKwM,WAC/BxM,KAAKmM,QAELnM,KAAK0M,YAAa,EAElB1M,KAAKsO,2BAA2BO,GAChC7O,KAAKyM,eAAiBmC,EAEtB,MAAMK,EAAuBN,EAnSR,sBADF,oBAqSbO,EAAiBP,EAnSH,qBACA,qBAoSpBC,EAAY7U,UAAUuQ,IAAI4E,GAE1BvU,EAAOiU,GAEPpR,EAAczD,UAAUuQ,IAAI2E,GAC5BL,EAAY7U,UAAUuQ,IAAI2E,GAa1BjP,KAAK6F,eAXoBsJ,KACvBP,EAAY7U,UAAUxC,OAAO0X,EAAsBC,GACnDN,EAAY7U,UAAUuQ,IAAIoB,IAE1BlO,EAAczD,UAAUxC,OAAOmU,GAAmBwD,EAAgBD,GAElEjP,KAAK0M,YAAa,EAElBoC,EAAa5D,KAGuB1N,EAAewC,KAAKoP,eAEtDJ,GACFhP,KAAK+M,OAET,CAEAqC,cACE,OAAOpP,KAAKsF,SAASvL,UAAUC,SAlUV,QAmUvB,CAEA4T,aACE,OAAOnH,EAAeG,QAAQiF,GAAsB7L,KAAKsF,SAC3D,CAEAmI,YACE,OAAOhH,EAAetH,KAAKyM,GAAe5L,KAAKsF,SACjD,CAEA6H,iBACMnN,KAAKwM,YACP6C,cAAcrP,KAAKwM,WACnBxM,KAAKwM,UAAY,KAErB,CAEA2B,kBAAkB9D,GAChB,OAAIpP,IACKoP,IAAcU,GAAiBD,GAAaD,GAG9CR,IAAcU,GAAiBF,GAAaC,EACrD,CAEAiE,kBAAkBlB,GAChB,OAAI5S,IACK4S,IAAU/C,GAAaC,GAAiBC,GAG1C6C,IAAU/C,GAAaE,GAAkBD,EAClD,CAGA,sBAAOpP,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAO+D,GAASvG,oBAAoBhG,KAAMsE,GAEhD,GAAsB,iBAAXA,GAKX,GAAsB,iBAAXA,EAAqB,CAC9B,QAAqBmE,IAAjBD,EAAKlE,IAAyBA,EAAO7C,WAAW,MAAmB,gBAAX6C,EAC1D,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IACP,OAVEkE,EAAK+E,GAAGjJ,EAWZ,EACF,EAOF/D,EAAac,GAAGpI,SAAUuS,GAlXE,sCAkXyC,SAAUpM,GAC7E,MAAMjC,EAASsJ,EAAekB,uBAAuB3H,MAErD,IAAK7C,IAAWA,EAAOpD,UAAUC,SAASyR,IACxC,OAGFrM,EAAMmD,iBAEN,MAAM+M,EAAW/C,GAASvG,oBAAoB7I,GACxCoS,EAAavP,KAAK7F,aAAa,oBAErC,OAAIoV,GACFD,EAAS/B,GAAGgC,QACZD,EAAShC,qBAIyC,SAAhDlK,EAAYY,iBAAiBhE,KAAM,UACrCsP,EAASjI,YACTiI,EAAShC,sBAIXgC,EAASpI,YACToI,EAAShC,oBACX,GAEA/M,EAAac,GAAGzJ,OAAQ2T,GAAqB,KAC3C,MAAMiE,EAAY/I,EAAetH,KA9YR,6BAgZzB,IAAK,MAAMmQ,KAAYE,EACrBjD,GAASvG,oBAAoBsJ,KAQjCnU,EAAmBoR,ICncnB,MAEM7G,GAAY,eAGZ+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtBiK,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB8F,GAAuB,QAAQ9F,cAE/BmK,GAAkB,OAClBC,GAAsB,WACtBC,GAAwB,aAExBC,GAA6B,WAAWF,OAAwBA,KAOhEpH,GAAuB,8BAEvBxE,GAAU,CACd+L,OAAQ,KACRrH,QAAQ,GAGJzE,GAAc,CAClB8L,OAAQ,iBACRrH,OAAQ,WAOV,MAAMsH,WAAiB9K,EACrBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAEftE,KAAKmQ,kBAAmB,EACxBnQ,KAAKoQ,cAAgB,GAErB,MAAMC,EAAa5J,EAAetH,KAAKuJ,IAEvC,IAAK,MAAM4H,KAAQD,EAAY,CAC7B,MAAM1Y,EAAW8O,EAAeiB,uBAAuB4I,GACjDC,EAAgB9J,EAAetH,KAAKxH,GACvCkM,OAAO2M,GAAgBA,IAAiBxQ,KAAKsF,UAE/B,OAAb3N,GAAqB4Y,EAAcvX,QACrCgH,KAAKoQ,cAAcpU,KAAKsU,EAE5B,CAEAtQ,KAAKyQ,sBAEAzQ,KAAKuF,QAAQ0K,QAChBjQ,KAAK0Q,0BAA0B1Q,KAAKoQ,cAAepQ,KAAK2Q,YAGtD3Q,KAAKuF,QAAQqD,QACf5I,KAAK4I,QAET,CAGA,kBAAW1E,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MA9ES,UA+EX,CAGAoN,SACM5I,KAAK2Q,WACP3Q,KAAK4Q,OAEL5Q,KAAK6Q,MAET,CAEAA,OACE,GAAI7Q,KAAKmQ,kBAAoBnQ,KAAK2Q,WAChC,OAGF,IAAIG,EAAiB,GASrB,GANI9Q,KAAKuF,QAAQ0K,SACfa,EAAiB9Q,KAAK+Q,uBA9EH,wCA+EhBlN,OAAOlN,GAAWA,IAAYqJ,KAAKsF,UACnCgB,IAAI3P,GAAWuZ,GAASlK,oBAAoBrP,EAAS,CAAEiS,QAAQ,MAGhEkI,EAAe9X,QAAU8X,EAAe,GAAGX,iBAC7C,OAIF,GADmB5P,EAAasB,QAAQ7B,KAAKsF,SAAUmK,IACxCxN,iBACb,OAGF,IAAK,MAAM+O,KAAkBF,EAC3BE,EAAeJ,OAGjB,MAAMK,EAAYjR,KAAKkR,gBAEvBlR,KAAKsF,SAASvL,UAAUxC,OAAOuY,IAC/B9P,KAAKsF,SAASvL,UAAUuQ,IAAIyF,IAE5B/P,KAAKsF,SAAS6L,MAAMF,GAAa,EAEjCjR,KAAK0Q,0BAA0B1Q,KAAKoQ,eAAe,GACnDpQ,KAAKmQ,kBAAmB,EAExB,MAYMiB,EAAa,SADUH,EAAU,GAAG9L,cAAgB8L,EAAUtP,MAAM,KAG1E3B,KAAK6F,eAdYwL,KACfrR,KAAKmQ,kBAAmB,EAExBnQ,KAAKsF,SAASvL,UAAUxC,OAAOwY,IAC/B/P,KAAKsF,SAASvL,UAAUuQ,IAAIwF,GAAqBD,IAEjD7P,KAAKsF,SAAS6L,MAAMF,GAAa,GAEjC1Q,EAAasB,QAAQ7B,KAAKsF,SAAUoK,KAMR1P,KAAKsF,UAAU,GAC7CtF,KAAKsF,SAAS6L,MAAMF,GAAa,GAAGjR,KAAKsF,SAAS8L,MACpD,CAEAR,OACE,GAAI5Q,KAAKmQ,mBAAqBnQ,KAAK2Q,WACjC,OAIF,GADmBpQ,EAAasB,QAAQ7B,KAAKsF,SAAUqK,IACxC1N,iBACb,OAGF,MAAMgP,EAAYjR,KAAKkR,gBAEvBlR,KAAKsF,SAAS6L,MAAMF,GAAa,GAAGjR,KAAKsF,SAASgM,wBAAwBL,OAE1EtW,EAAOqF,KAAKsF,UAEZtF,KAAKsF,SAASvL,UAAUuQ,IAAIyF,IAC5B/P,KAAKsF,SAASvL,UAAUxC,OAAOuY,GAAqBD,IAEpD,IAAK,MAAMhO,KAAW7B,KAAKoQ,cAAe,CACxC,MAAMzZ,EAAU8P,EAAekB,uBAAuB9F,GAElDlL,IAAYqJ,KAAK2Q,SAASha,IAC5BqJ,KAAK0Q,0BAA0B,CAAC7O,IAAU,EAE9C,CAEA7B,KAAKmQ,kBAAmB,EASxBnQ,KAAKsF,SAAS6L,MAAMF,GAAa,GAEjCjR,KAAK6F,eATYwL,KACfrR,KAAKmQ,kBAAmB,EACxBnQ,KAAKsF,SAASvL,UAAUxC,OAAOwY,IAC/B/P,KAAKsF,SAASvL,UAAUuQ,IAAIwF,IAC5BvP,EAAasB,QAAQ7B,KAAKsF,SAAUsK,KAKR5P,KAAKsF,UAAU,EAC/C,CAGAqL,SAASha,EAAUqJ,KAAKsF,UACtB,OAAO3O,EAAQoD,UAAUC,SAAS6V,GACpC,CAEArL,kBAAkBF,GAGhB,OAFAA,EAAOsE,OAAS9H,QAAQwD,EAAOsE,QAC/BtE,EAAO2L,OAASlX,EAAWuL,EAAO2L,QAC3B3L,CACT,CAEA4M,gBACE,OAAOlR,KAAKsF,SAASvL,UAAUC,SAtLL,uBAEhB,QACC,QAoLb,CAEAyW,sBACE,IAAKzQ,KAAKuF,QAAQ0K,OAChB,OAGF,MAAMpJ,EAAW7G,KAAK+Q,uBAAuBrI,IAE7C,IAAK,MAAM/R,KAAWkQ,EAAU,CAC9B,MAAM0K,EAAW9K,EAAekB,uBAAuBhR,GAEnD4a,GACFvR,KAAK0Q,0BAA0B,CAAC/Z,GAAUqJ,KAAK2Q,SAASY,GAE5D,CACF,CAEAR,uBAAuBpZ,GACrB,MAAMkP,EAAWJ,EAAetH,KAAK6Q,GAA4BhQ,KAAKuF,QAAQ0K,QAE9E,OAAOxJ,EAAetH,KAAKxH,EAAUqI,KAAKuF,QAAQ0K,QAAQpM,OAAOlN,IAAYkQ,EAASzF,SAASzK,GACjG,CAEA+Z,0BAA0Bc,EAAcC,GACtC,GAAKD,EAAaxY,OAIlB,IAAK,MAAMrC,KAAW6a,EACpB7a,EAAQoD,UAAU6O,OAvNK,aAuNyB6I,GAChD9a,EAAQ2M,aAAa,gBAAiBmO,EAE1C,CAGA,sBAAO9V,CAAgB2I,GACrB,MAAMiB,EAAU,GAKhB,MAJsB,iBAAXjB,GAAuB,YAAYW,KAAKX,KACjDiB,EAAQqD,QAAS,GAGZ5I,KAAKuI,KAAK,WACf,MAAMC,EAAO0H,GAASlK,oBAAoBhG,KAAMuF,GAEhD,GAAsB,iBAAXjB,EAAqB,CAC9B,QAA4B,IAAjBkE,EAAKlE,GACd,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IACP,CACF,EACF,EAOF/D,EAAac,GAAGpI,SAAUuS,GAAsB9C,GAAsB,SAAUtJ,IAEjD,MAAzBA,EAAMjC,OAAO8K,SAAoB7I,EAAMW,gBAAmD,MAAjCX,EAAMW,eAAekI,UAChF7I,EAAMmD,iBAGR,IAAK,MAAM5L,KAAW8P,EAAemB,gCAAgC5H,MACnEkQ,GAASlK,oBAAoBrP,EAAS,CAAEiS,QAAQ,IAASA,QAE7D,GAMAzN,EAAmB+U,IC1QnB,MAAM1U,GAAO,WAEPkK,GAAY,eACZgF,GAAe,YAIfgH,GAAe,UACfC,GAAiB,YAGjBhC,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtB8F,GAAuB,QAAQ9F,KAAYgF,KAC3CkH,GAAyB,UAAUlM,KAAYgF,KAC/CmH,GAAuB,QAAQnM,KAAYgF,KAE3CmF,GAAkB,OAOlBnH,GAAuB,4DACvBoJ,GAA6B,GAAGpJ,MAAwBmH,KACxDkC,GAAgB,iBAKhBC,GAAgB/W,IAAU,UAAY,YACtCgX,GAAmBhX,IAAU,YAAc,UAC3CiX,GAAmBjX,IAAU,aAAe,eAC5CkX,GAAsBlX,IAAU,eAAiB,aACjDmX,GAAkBnX,IAAU,aAAe,cAC3CoX,GAAiBpX,IAAU,cAAgB,aAI3CiJ,GAAU,CACdoO,WAAW,EACXC,SAAU,kBACVC,QAAS,UACTC,OAAQ,CAAC,EAAG,GACZC,aAAc,KACdC,UAAW,UAGPxO,GAAc,CAClBmO,UAAW,mBACXC,SAAU,mBACVC,QAAS,SACTC,OAAQ,0BACRC,aAAc,yBACdC,UAAW,2BAOb,MAAMC,WAAiBxN,EACrBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAEftE,KAAK6S,QAAU,KACf7S,KAAK8S,QAAU9S,KAAKsF,SAAS3L,WAE7BqG,KAAK+S,MAAQtM,EAAeY,KAAKrH,KAAKsF,SAAUyM,IAAe,IAC7DtL,EAAeS,KAAKlH,KAAKsF,SAAUyM,IAAe,IAClDtL,EAAeG,QAAQmL,GAAe/R,KAAK8S,SAC7C9S,KAAKgT,UAAYhT,KAAKiT,eACxB,CAGA,kBAAW/O,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,OAAOA,EACT,CAGAoN,SACE,OAAO5I,KAAK2Q,WAAa3Q,KAAK4Q,OAAS5Q,KAAK6Q,MAC9C,CAEAA,OACE,GAAIjX,EAAWoG,KAAKsF,WAAatF,KAAK2Q,WACpC,OAGF,MAAM7Q,EAAgB,CACpBA,cAAeE,KAAKsF,UAKtB,IAFkB/E,EAAasB,QAAQ7B,KAAKsF,SAAUmK,GAAY3P,GAEpDmC,iBAAd,CAUA,GANAjC,KAAKkT,gBAMD,iBAAkBja,SAASoB,kBAAoB2F,KAAK8S,QAAQrZ,QAtFxC,eAuFtB,IAAK,MAAM9C,IAAW,GAAG+P,UAAUzN,SAAS8B,KAAK8L,UAC/CtG,EAAac,GAAG1K,EAAS,YAAa+D,GAI1CsF,KAAKsF,SAAS6N,QACdnT,KAAKsF,SAAShC,aAAa,iBAAiB,GAE5CtD,KAAK+S,MAAMhZ,UAAUuQ,IAAIuF,IACzB7P,KAAKsF,SAASvL,UAAUuQ,IAAIuF,IAC5BtP,EAAasB,QAAQ7B,KAAKsF,SAAUoK,GAAa5P,EAnBjD,CAoBF,CAEA8Q,OACE,GAAIhX,EAAWoG,KAAKsF,YAActF,KAAK2Q,WACrC,OAGF,MAAM7Q,EAAgB,CACpBA,cAAeE,KAAKsF,UAGtBtF,KAAKoT,cAActT,EACrB,CAEA2F,UACMzF,KAAK6S,SACP7S,KAAK6S,QAAQQ,UAGfhO,MAAMI,SACR,CAEA6N,SACEtT,KAAKgT,UAAYhT,KAAKiT,gBAClBjT,KAAK6S,SACP7S,KAAK6S,QAAQS,QAEjB,CAGAF,cAActT,GAEZ,IADkBS,EAAasB,QAAQ7B,KAAKsF,SAAUqK,GAAY7P,GACpDmC,iBAAd,CAMA,GAAI,iBAAkBhJ,SAASoB,gBAC7B,IAAK,MAAM1D,IAAW,GAAG+P,UAAUzN,SAAS8B,KAAK8L,UAC/CtG,EAAaC,IAAI7J,EAAS,YAAa+D,GAIvCsF,KAAK6S,SACP7S,KAAK6S,QAAQQ,UAGfrT,KAAK+S,MAAMhZ,UAAUxC,OAAOsY,IAC5B7P,KAAKsF,SAASvL,UAAUxC,OAAOsY,IAC/B7P,KAAKsF,SAAShC,aAAa,gBAAiB,SAC5CF,EAAYG,oBAAoBvD,KAAK+S,MAAO,UAC5CxS,EAAasB,QAAQ7B,KAAKsF,SAAUsK,GAAc9P,EAlBlD,CAmBF,CAEAuE,WAAWC,GAGT,GAAgC,iBAFhCA,EAASe,MAAMhB,WAAWC,IAERqO,YAA2B/Z,EAAU0L,EAAOqO,YACV,mBAA3CrO,EAAOqO,UAAUrB,sBAGxB,MAAM,IAAIpM,UAAU,GAAG1J,GAAK2J,+GAG9B,OAAOb,CACT,CAEA4O,gBACE,QAAsB,IAAXK,EACT,MAAM,IAAIrO,UAAU,yEAGtB,IAAIsO,EAAmBxT,KAAKsF,SAEG,WAA3BtF,KAAKuF,QAAQoN,UACfa,EAAmBxT,KAAK8S,QACfla,EAAUoH,KAAKuF,QAAQoN,WAChCa,EAAmBza,EAAWiH,KAAKuF,QAAQoN,WACA,iBAA3B3S,KAAKuF,QAAQoN,YAC7Ba,EAAmBxT,KAAKuF,QAAQoN,WAGlC,MAAMD,EAAe1S,KAAKyT,mBAC1BzT,KAAK6S,QAAUU,EAAOG,aAAaF,EAAkBxT,KAAK+S,MAAOL,EACnE,CAEA/B,WACE,OAAO3Q,KAAK+S,MAAMhZ,UAAUC,SAAS6V,GACvC,CAEA8D,gBACE,MAAMC,EAAiB5T,KAAK8S,QAE5B,GAAIc,EAAe7Z,UAAUC,SAzMN,WA0MrB,OAAOoY,GAGT,GAAIwB,EAAe7Z,UAAUC,SA5MJ,aA6MvB,OAAOqY,GAGT,GAAIuB,EAAe7Z,UAAUC,SA/MA,iBAgN3B,MAhMsB,MAmMxB,GAAI4Z,EAAe7Z,UAAUC,SAlNE,mBAmN7B,MAnMyB,SAuM3B,MAAM6Z,EAAkF,QAA1Eva,iBAAiB0G,KAAK+S,OAAOxZ,iBAAiB,iBAAiB8M,OAE7E,OAAIuN,EAAe7Z,UAAUC,SA7NP,UA8Nb6Z,EAAQ5B,GAAmBD,GAG7B6B,EAAQ1B,GAAsBD,EACvC,CAEAe,gBACE,OAAkD,OAA3CjT,KAAKsF,SAAS7L,QA5ND,UA6NtB,CAEAqa,aACE,MAAMrB,OAAEA,GAAWzS,KAAKuF,QAExB,MAAsB,iBAAXkN,EACFA,EAAO1V,MAAM,KAAKuJ,IAAI5D,GAAS9F,OAAO8R,SAAShM,EAAO,KAGzC,mBAAX+P,EACFsB,GAActB,EAAOsB,EAAY/T,KAAKsF,UAGxCmN,CACT,CAEAgB,mBACE,MAAMO,EAAwB,CAC5BC,UAAWjU,KAAK2T,gBAChBO,UAAW,CAAC,CACV3Y,KAAM,kBACN4Y,QAAS,CACP5B,SAAUvS,KAAKuF,QAAQgN,WAG3B,CACEhX,KAAM,SACN4Y,QAAS,CACP1B,OAAQzS,KAAK8T,iBAcnB,OARI9T,KAAKgT,WAAsC,WAAzBhT,KAAKuF,QAAQiN,WACjCpP,EAAYC,iBAAiBrD,KAAK+S,MAAO,SAAU,UACnDiB,EAAsBE,UAAY,CAAC,CACjC3Y,KAAM,cACN6Y,SAAS,KAIN,IACFJ,KACA/X,EAAQ+D,KAAKuF,QAAQmN,aAAc,MAACjK,EAAWuL,IAEtD,CAEAK,iBAAgBzd,IAAEA,EAAGuG,OAAEA,IACrB,MAAMqQ,EAAQ/G,EAAetH,KA5QF,8DA4Q+Ba,KAAK+S,OAAOlP,OAAOlN,GAAWwC,EAAUxC,IAE7F6W,EAAMxU,QAMXsE,EAAqBkQ,EAAOrQ,EAAQvG,IAAQ+a,IAAiBnE,EAAMpM,SAASjE,IAASgW,OACvF,CAGA,sBAAOxX,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOoK,GAAS5M,oBAAoBhG,KAAMsE,GAEhD,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBkE,EAAKlE,GACd,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IANL,CAOF,EACF,CAEA,iBAAOgQ,CAAWlV,GAChB,GA/TuB,IA+TnBA,EAAMyJ,QAAiD,UAAfzJ,EAAMqB,MAlUtC,QAkU0DrB,EAAMxI,IAC1E,OAGF,MAAM2d,EAAc9N,EAAetH,KAAK2S,IAExC,IAAK,MAAMlJ,KAAU2L,EAAa,CAChC,MAAMC,EAAU5B,GAAS7M,YAAY6C,GACrC,IAAK4L,IAAyC,IAA9BA,EAAQjP,QAAQ+M,UAC9B,SAGF,MAAMmC,EAAerV,EAAMqV,eACrBC,EAAeD,EAAarT,SAASoT,EAAQzB,OACnD,GACE0B,EAAarT,SAASoT,EAAQlP,WACC,WAA9BkP,EAAQjP,QAAQ+M,YAA2BoC,GACb,YAA9BF,EAAQjP,QAAQ+M,WAA2BoC,EAE5C,SAIF,GAAIF,EAAQzB,MAAM/Y,SAASoF,EAAMjC,UAA4B,UAAfiC,EAAMqB,MAzV1C,QAyV8DrB,EAAMxI,KAAoB,qCAAqCqO,KAAK7F,EAAMjC,OAAO8K,UACvJ,SAGF,MAAMnI,EAAgB,CAAEA,cAAe0U,EAAQlP,UAE5B,UAAflG,EAAMqB,OACRX,EAAckI,WAAa5I,GAG7BoV,EAAQpB,cAActT,EACxB,CACF,CAEA,4BAAO6U,CAAsBvV,GAI3B,MAAMwV,EAAU,kBAAkB3P,KAAK7F,EAAMjC,OAAO8K,SAC9C4M,EA7WS,WA6WOzV,EAAMxI,IACtBke,EAAkB,CAACpD,GAAcC,IAAgBvQ,SAAShC,EAAMxI,KAEtE,IAAKke,IAAoBD,EACvB,OAGF,GAAID,IAAYC,EACd,OAGFzV,EAAMmD,iBAGN,MAAMwS,EAAkB/U,KAAK+G,QAAQ2B,IACnC1I,KACCyG,EAAeS,KAAKlH,KAAM0I,IAAsB,IAC/CjC,EAAeY,KAAKrH,KAAM0I,IAAsB,IAChDjC,EAAeG,QAAQ8B,GAAsBtJ,EAAMW,eAAepG,YAEhE9C,EAAW+b,GAAS5M,oBAAoB+O,GAE9C,GAAID,EAIF,OAHA1V,EAAM4V,kBACNne,EAASga,YACTha,EAASwd,gBAAgBjV,GAIvBvI,EAAS8Z,aACXvR,EAAM4V,kBACNne,EAAS+Z,OACTmE,EAAgB5B,QAEpB,EAOF5S,EAAac,GAAGpI,SAAU2Y,GAAwBlJ,GAAsBkK,GAAS+B,uBACjFpU,EAAac,GAAGpI,SAAU2Y,GAAwBG,GAAea,GAAS+B,uBAC1EpU,EAAac,GAAGpI,SAAUuS,GAAsBoH,GAAS0B,YACzD/T,EAAac,GAAGpI,SAAU4Y,GAAsBe,GAAS0B,YACzD/T,EAAac,GAAGpI,SAAUuS,GAAsB9C,GAAsB,SAAUtJ,GAC9EA,EAAMmD,iBACNqQ,GAAS5M,oBAAoBhG,MAAM4I,QACrC,GAMAzN,EAAmByX,ICnbnB,MAAMpX,GAAO,WAEPqU,GAAkB,OAClBoF,GAAkB,gBAAgBzZ,KAElC0I,GAAU,CACdgR,UAAW,iBACXC,cAAe,KACfrP,YAAY,EACZ3M,WAAW,EACXic,YAAa,QAGTjR,GAAc,CAClB+Q,UAAW,SACXC,cAAe,kBACfrP,WAAY,UACZ3M,UAAW,UACXic,YAAa,oBAOf,MAAMC,WAAiBpR,EACrBU,YAAYL,GACVe,QACArF,KAAKuF,QAAUvF,KAAKqE,WAAWC,GAC/BtE,KAAKsV,aAAc,EACnBtV,KAAKsF,SAAW,IAClB,CAGA,kBAAWpB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,OAAOA,EACT,CAGAqV,KAAKxV,GACH,IAAK2E,KAAKuF,QAAQpM,UAEhB,YADA8C,EAAQZ,GAIV2E,KAAKuV,UAEL,MAAM5e,EAAUqJ,KAAKwV,cACjBxV,KAAKuF,QAAQO,YACfnL,EAAOhE,GAGTA,EAAQoD,UAAUuQ,IAAIuF,IAEtB7P,KAAKyV,kBAAkB,KACrBxZ,EAAQZ,IAEZ,CAEAuV,KAAKvV,GACE2E,KAAKuF,QAAQpM,WAKlB6G,KAAKwV,cAAczb,UAAUxC,OAAOsY,IAEpC7P,KAAKyV,kBAAkB,KACrBzV,KAAKyF,UACLxJ,EAAQZ,MARRY,EAAQZ,EAUZ,CAEAoK,UACOzF,KAAKsV,cAIV/U,EAAaC,IAAIR,KAAKsF,SAAU2P,IAEhCjV,KAAKsF,SAAS/N,SACdyI,KAAKsV,aAAc,EACrB,CAGAE,cACE,IAAKxV,KAAKsF,SAAU,CAClB,MAAMoQ,EAAWzc,SAAS0c,cAAc,OACxCD,EAASR,UAAYlV,KAAKuF,QAAQ2P,UAC9BlV,KAAKuF,QAAQO,YACf4P,EAAS3b,UAAUuQ,IAjGH,QAoGlBtK,KAAKsF,SAAWoQ,CAClB,CAEA,OAAO1V,KAAKsF,QACd,CAEAd,kBAAkBF,GAGhB,OADAA,EAAO8Q,YAAcrc,EAAWuL,EAAO8Q,aAChC9Q,CACT,CAEAiR,UACE,GAAIvV,KAAKsV,YACP,OAGF,MAAM3e,EAAUqJ,KAAKwV,cACrBxV,KAAKuF,QAAQ6P,YAAYQ,OAAOjf,GAEhC4J,EAAac,GAAG1K,EAASse,GAAiB,KACxChZ,EAAQ+D,KAAKuF,QAAQ4P,iBAGvBnV,KAAKsV,aAAc,CACrB,CAEAG,kBAAkBpa,GAChBgB,EAAuBhB,EAAU2E,KAAKwV,cAAexV,KAAKuF,QAAQO,WACpE,ECpIF,MAEMJ,GAAY,gBACZmQ,GAAgB,UAAUnQ,KAC1BoQ,GAAoB,cAAcpQ,KAIlCqQ,GAAmB,WAEnB7R,GAAU,CACd8R,WAAW,EACXC,YAAa,MAGT9R,GAAc,CAClB6R,UAAW,UACXC,YAAa,WAOf,MAAMC,WAAkBjS,EACtBU,YAAYL,GACVe,QACArF,KAAKuF,QAAUvF,KAAKqE,WAAWC,GAC/BtE,KAAKmW,WAAY,EACjBnW,KAAKoW,qBAAuB,IAC9B,CAGA,kBAAWlS,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MA1CS,WA2CX,CAGA6a,WACMrW,KAAKmW,YAILnW,KAAKuF,QAAQyQ,WACfhW,KAAKuF,QAAQ0Q,YAAY9C,QAG3B5S,EAAaC,IAAIvH,SAAUyM,IAC3BnF,EAAac,GAAGpI,SAAU4c,GAAezW,GAASY,KAAKsW,eAAelX,IACtEmB,EAAac,GAAGpI,SAAU6c,GAAmB1W,GAASY,KAAKuW,eAAenX,IAE1EY,KAAKmW,WAAY,EACnB,CAEAK,aACOxW,KAAKmW,YAIVnW,KAAKmW,WAAY,EACjB5V,EAAaC,IAAIvH,SAAUyM,IAC7B,CAGA4Q,eAAelX,GACb,MAAM6W,YAAEA,GAAgBjW,KAAKuF,QAE7B,GAAInG,EAAMjC,SAAWlE,UAAYmG,EAAMjC,SAAW8Y,GAAeA,EAAYjc,SAASoF,EAAMjC,QAC1F,OAGF,MAAMsZ,EAAWhQ,EAAec,kBAAkB0O,GAE1B,IAApBQ,EAASzd,OACXid,EAAY9C,QACHnT,KAAKoW,uBAAyBL,GACvCU,EAASA,EAASzd,OAAS,GAAGma,QAE9BsD,EAAS,GAAGtD,OAEhB,CAEAoD,eAAenX,GApFD,QAqFRA,EAAMxI,MAIVoJ,KAAKoW,qBAAuBhX,EAAMsX,SAAWX,GAxFzB,UAyFtB,EChGF,MAAMY,GAAyB,oDACzBC,GAA0B,cAC1BC,GAAmB,gBACnBC,GAAkB,eAMxB,MAAMC,GACJpS,cACE3E,KAAKsF,SAAWrM,SAAS8B,IAC3B,CAGAic,WAEE,MAAMC,EAAgBhe,SAASoB,gBAAgB6c,YAC/C,OAAOpZ,KAAKsM,IAAIxS,OAAOuf,WAAaF,EACtC,CAEArG,OACE,MAAMwG,EAAQpX,KAAKgX,WACnBhX,KAAKqX,mBAELrX,KAAKsX,sBAAsBtX,KAAKsF,SAAUuR,GAAkBU,GAAmBA,EAAkBH,GAEjGpX,KAAKsX,sBAAsBX,GAAwBE,GAAkBU,GAAmBA,EAAkBH,GAC1GpX,KAAKsX,sBAAsBV,GAAyBE,GAAiBS,GAAmBA,EAAkBH,EAC5G,CAEAI,QACExX,KAAKyX,wBAAwBzX,KAAKsF,SAAU,YAC5CtF,KAAKyX,wBAAwBzX,KAAKsF,SAAUuR,IAC5C7W,KAAKyX,wBAAwBd,GAAwBE,IACrD7W,KAAKyX,wBAAwBb,GAAyBE,GACxD,CAEAY,gBACE,OAAO1X,KAAKgX,WAAa,CAC3B,CAGAK,mBACErX,KAAK2X,sBAAsB3X,KAAKsF,SAAU,YAC1CtF,KAAKsF,SAAS6L,MAAMyG,SAAW,QACjC,CAEAN,sBAAsB3f,EAAUkgB,EAAexc,GAC7C,MAAMyc,EAAiB9X,KAAKgX,WAW5BhX,KAAK+X,2BAA2BpgB,EAVHhB,IAC3B,GAAIA,IAAYqJ,KAAKsF,UAAY1N,OAAOuf,WAAaxgB,EAAQugB,YAAcY,EACzE,OAGF9X,KAAK2X,sBAAsBhhB,EAASkhB,GACpC,MAAMN,EAAkB3f,OAAO0B,iBAAiB3C,GAAS4C,iBAAiBse,GAC1ElhB,EAAQwa,MAAM6G,YAAYH,EAAe,GAAGxc,EAASuB,OAAOC,WAAW0a,UAI3E,CAEAI,sBAAsBhhB,EAASkhB,GAC7B,MAAMI,EAActhB,EAAQwa,MAAM5X,iBAAiBse,GAC/CI,GACF7U,EAAYC,iBAAiB1M,EAASkhB,EAAeI,EAEzD,CAEAR,wBAAwB9f,EAAUkgB,GAahC7X,KAAK+X,2BAA2BpgB,EAZHhB,IAC3B,MAAM+L,EAAQU,EAAYY,iBAAiBrN,EAASkhB,GAEtC,OAAVnV,GAKJU,EAAYG,oBAAoB5M,EAASkhB,GACzClhB,EAAQwa,MAAM6G,YAAYH,EAAenV,IALvC/L,EAAQwa,MAAM+G,eAAeL,IASnC,CAEAE,2BAA2BpgB,EAAUwgB,GACnC,GAAIvf,EAAUjB,GACZwgB,EAASxgB,QAIX,IAAK,MAAM4O,KAAOE,EAAetH,KAAKxH,EAAUqI,KAAKsF,UACnD6S,EAAS5R,EAEb,ECxFF,MAEMb,GAAY,YAIZiK,GAAa,OAAOjK,KACpB0S,GAAuB,gBAAgB1S,KACvCkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtB2S,GAAe,SAAS3S,KACxB4S,GAAsB,gBAAgB5S,KACtC6S,GAA0B,oBAAoB7S,KAC9C8S,GAAwB,kBAAkB9S,KAC1C8F,GAAuB,QAAQ9F,cAE/B+S,GAAkB,aAElB5I,GAAkB,OAClB6I,GAAoB,eAOpBxU,GAAU,CACdwR,UAAU,EACVvC,OAAO,EACPjH,UAAU,GAGN/H,GAAc,CAClBuR,SAAU,mBACVvC,MAAO,UACPjH,SAAU,WAOZ,MAAMyM,WAAcvT,EAClBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAEftE,KAAK4Y,QAAUnS,EAAeG,QAxBV,gBAwBmC5G,KAAKsF,UAC5DtF,KAAK6Y,UAAY7Y,KAAK8Y,sBACtB9Y,KAAK+Y,WAAa/Y,KAAKgZ,uBACvBhZ,KAAK2Q,UAAW,EAChB3Q,KAAKmQ,kBAAmB,EACxBnQ,KAAKiZ,WAAa,IAAIlC,GAEtB/W,KAAK8M,oBACP,CAGA,kBAAW5I,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MAnES,OAoEX,CAGAoN,OAAO9I,GACL,OAAOE,KAAK2Q,SAAW3Q,KAAK4Q,OAAS5Q,KAAK6Q,KAAK/Q,EACjD,CAEA+Q,KAAK/Q,GACCE,KAAK2Q,UAAY3Q,KAAKmQ,kBAIR5P,EAAasB,QAAQ7B,KAAKsF,SAAUmK,GAAY,CAChE3P,kBAGYmC,mBAIdjC,KAAK2Q,UAAW,EAChB3Q,KAAKmQ,kBAAmB,EAExBnQ,KAAKiZ,WAAWrI,OAEhB3X,SAAS8B,KAAKhB,UAAUuQ,IAAImO,IAE5BzY,KAAKkZ,gBAELlZ,KAAK6Y,UAAUhI,KAAK,IAAM7Q,KAAKmZ,aAAarZ,IAC9C,CAEA8Q,OACO5Q,KAAK2Q,WAAY3Q,KAAKmQ,mBAIT5P,EAAasB,QAAQ7B,KAAKsF,SAAUqK,IAExC1N,mBAIdjC,KAAK2Q,UAAW,EAChB3Q,KAAKmQ,kBAAmB,EACxBnQ,KAAK+Y,WAAWvC,aAEhBxW,KAAKsF,SAASvL,UAAUxC,OAAOsY,IAE/B7P,KAAK6F,eAAe,IAAM7F,KAAKoZ,aAAcpZ,KAAKsF,SAAUtF,KAAKoP,gBACnE,CAEA3J,UACElF,EAAaC,IAAI5I,OAAQ8N,IACzBnF,EAAaC,IAAIR,KAAK4Y,QAASlT,IAE/B1F,KAAK6Y,UAAUpT,UACfzF,KAAK+Y,WAAWvC,aAEhBnR,MAAMI,SACR,CAEA4T,eACErZ,KAAKkZ,eACP,CAGAJ,sBACE,OAAO,IAAIzD,GAAS,CAClBlc,UAAW2H,QAAQd,KAAKuF,QAAQmQ,UAChC5P,WAAY9F,KAAKoP,eAErB,CAEA4J,uBACE,OAAO,IAAI9C,GAAU,CACnBD,YAAajW,KAAKsF,UAEtB,CAEA6T,aAAarZ,GAEN7G,SAAS8B,KAAKf,SAASgG,KAAKsF,WAC/BrM,SAAS8B,KAAK6a,OAAO5V,KAAKsF,UAG5BtF,KAAKsF,SAAS6L,MAAMqB,QAAU,QAC9BxS,KAAKsF,SAAS9B,gBAAgB,eAC9BxD,KAAKsF,SAAShC,aAAa,cAAc,GACzCtD,KAAKsF,SAAShC,aAAa,OAAQ,UACnCtD,KAAKsF,SAASgU,UAAY,EAE1B,MAAMC,EAAY9S,EAAeG,QAxIT,cAwIsC5G,KAAK4Y,SAC/DW,IACFA,EAAUD,UAAY,GAGxB3e,EAAOqF,KAAKsF,UAEZtF,KAAKsF,SAASvL,UAAUuQ,IAAIuF,IAa5B7P,KAAK6F,eAXsB2T,KACrBxZ,KAAKuF,QAAQ4N,OACfnT,KAAK+Y,WAAW1C,WAGlBrW,KAAKmQ,kBAAmB,EACxB5P,EAAasB,QAAQ7B,KAAKsF,SAAUoK,GAAa,CAC/C5P,mBAIoCE,KAAK4Y,QAAS5Y,KAAKoP,cAC7D,CAEAtC,qBACEvM,EAAac,GAAGrB,KAAKsF,SAAUkT,GAAuBpZ,IApLvC,WAqLTA,EAAMxI,MAINoJ,KAAKuF,QAAQ2G,SACflM,KAAK4Q,OAIP5Q,KAAKyZ,gCAGPlZ,EAAac,GAAGzJ,OAAQygB,GAAc,KAChCrY,KAAK2Q,WAAa3Q,KAAKmQ,kBACzBnQ,KAAKkZ,kBAIT3Y,EAAac,GAAGrB,KAAKsF,SAAUiT,GAAyBnZ,IAEtDmB,EAAae,IAAItB,KAAKsF,SAAUgT,GAAqBoB,IAC/C1Z,KAAKsF,WAAalG,EAAMjC,QAAU6C,KAAKsF,WAAaoU,EAAOvc,SAIjC,WAA1B6C,KAAKuF,QAAQmQ,SAKb1V,KAAKuF,QAAQmQ,UACf1V,KAAK4Q,OALL5Q,KAAKyZ,iCASb,CAEAL,aACEpZ,KAAKsF,SAAS6L,MAAMqB,QAAU,OAC9BxS,KAAKsF,SAAShC,aAAa,eAAe,GAC1CtD,KAAKsF,SAAS9B,gBAAgB,cAC9BxD,KAAKsF,SAAS9B,gBAAgB,QAC9BxD,KAAKmQ,kBAAmB,EAExBnQ,KAAK6Y,UAAUjI,KAAK,KAClB3X,SAAS8B,KAAKhB,UAAUxC,OAAOkhB,IAC/BzY,KAAK2Z,oBACL3Z,KAAKiZ,WAAWzB,QAChBjX,EAAasB,QAAQ7B,KAAKsF,SAAUsK,KAExC,CAEAR,cACE,OAAOpP,KAAKsF,SAASvL,UAAUC,SA5NX,OA6NtB,CAEAyf,6BAEE,GADkBlZ,EAAasB,QAAQ7B,KAAKsF,SAAU8S,IACxCnW,iBACZ,OAGF,MAAM2X,EAAqB5Z,KAAKsF,SAASuU,aAAe5gB,SAASoB,gBAAgByf,aAC3EC,EAAmB/Z,KAAKsF,SAAS6L,MAAM6I,UAEpB,WAArBD,GAAiC/Z,KAAKsF,SAASvL,UAAUC,SAAS0e,MAIjEkB,IACH5Z,KAAKsF,SAAS6L,MAAM6I,UAAY,UAGlCha,KAAKsF,SAASvL,UAAUuQ,IAAIoO,IAC5B1Y,KAAK6F,eAAe,KAClB7F,KAAKsF,SAASvL,UAAUxC,OAAOmhB,IAC/B1Y,KAAK6F,eAAe,KAClB7F,KAAKsF,SAAS6L,MAAM6I,UAAYD,GAC/B/Z,KAAK4Y,UACP5Y,KAAK4Y,SAER5Y,KAAKsF,SAAS6N,QAChB,CAMA+F,gBACE,MAAMU,EAAqB5Z,KAAKsF,SAASuU,aAAe5gB,SAASoB,gBAAgByf,aAC3EhC,EAAiB9X,KAAKiZ,WAAWjC,WACjCiD,EAAoBnC,EAAiB,EAE3C,GAAImC,IAAsBL,EAAoB,CAC5C,MAAM/U,EAAW5J,IAAU,cAAgB,eAC3C+E,KAAKsF,SAAS6L,MAAMtM,GAAY,GAAGiT,KACrC,CAEA,IAAKmC,GAAqBL,EAAoB,CAC5C,MAAM/U,EAAW5J,IAAU,eAAiB,cAC5C+E,KAAKsF,SAAS6L,MAAMtM,GAAY,GAAGiT,KACrC,CACF,CAEA6B,oBACE3Z,KAAKsF,SAAS6L,MAAM+I,YAAc,GAClCla,KAAKsF,SAAS6L,MAAMgJ,aAAe,EACrC,CAGA,sBAAOxe,CAAgB2I,EAAQxE,GAC7B,OAAOE,KAAKuI,KAAK,WACf,MAAMC,EAAOmQ,GAAM3S,oBAAoBhG,KAAMsE,GAE7C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBkE,EAAKlE,GACd,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,GAAQxE,EANb,CAOF,EACF,EAOFS,EAAac,GAAGpI,SAAUuS,GAnSG,2BAmSyC,SAAUpM,GAC9E,MAAMjC,EAASsJ,EAAekB,uBAAuB3H,MAEjD,CAAC,IAAK,QAAQoB,SAASpB,KAAKiI,UAC9B7I,EAAMmD,iBAGRhC,EAAae,IAAInE,EAAQsS,GAAY2K,IAC/BA,EAAUnY,kBAKd1B,EAAae,IAAInE,EAAQyS,GAAc,KACjCzW,EAAU6G,OACZA,KAAKmT,YAMX,MAAMkH,EAAc5T,EAAeG,QA3Tf,eA4ThByT,GACF1B,GAAM5S,YAAYsU,GAAazJ,OAGpB+H,GAAM3S,oBAAoB7I,GAElCyL,OAAO5I,KACd,GAEA6H,EAAqB8Q,IAMrBxd,EAAmBwd,IC/VnB,MAEMjT,GAAY,gBACZgF,GAAe,YACfa,GAAsB,OAAO7F,KAAYgF,KAGzCmF,GAAkB,OAClByK,GAAqB,UACrBC,GAAoB,SAEpBC,GAAgB,kBAEhB/K,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtBiK,GAAa,OAAOjK,KACpB0S,GAAuB,gBAAgB1S,KACvCkK,GAAe,SAASlK,KACxB2S,GAAe,SAAS3S,KACxB8F,GAAuB,QAAQ9F,KAAYgF,KAC3C8N,GAAwB,kBAAkB9S,KAI1CxB,GAAU,CACdwR,UAAU,EACVxJ,UAAU,EACVuO,QAAQ,GAGJtW,GAAc,CAClBuR,SAAU,mBACVxJ,SAAU,UACVuO,OAAQ,WAOV,MAAMC,WAAkBtV,EACtBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAEftE,KAAK2Q,UAAW,EAChB3Q,KAAK6Y,UAAY7Y,KAAK8Y,sBACtB9Y,KAAK+Y,WAAa/Y,KAAKgZ,uBACvBhZ,KAAK8M,oBACP,CAGA,kBAAW5I,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MA5DS,WA6DX,CAGAoN,OAAO9I,GACL,OAAOE,KAAK2Q,SAAW3Q,KAAK4Q,OAAS5Q,KAAK6Q,KAAK/Q,EACjD,CAEA+Q,KAAK/Q,GACCE,KAAK2Q,UAISpQ,EAAasB,QAAQ7B,KAAKsF,SAAUmK,GAAY,CAAE3P,kBAEtDmC,mBAIdjC,KAAK2Q,UAAW,EAChB3Q,KAAK6Y,UAAUhI,OAEV7Q,KAAKuF,QAAQkV,SAChB,IAAI1D,IAAkBnG,OAGxB5Q,KAAKsF,SAAShC,aAAa,cAAc,GACzCtD,KAAKsF,SAAShC,aAAa,OAAQ,UACnCtD,KAAKsF,SAASvL,UAAUuQ,IAAIgQ,IAY5Bta,KAAK6F,eAVoBsJ,KAClBnP,KAAKuF,QAAQkV,SAAUza,KAAKuF,QAAQmQ,UACvC1V,KAAK+Y,WAAW1C,WAGlBrW,KAAKsF,SAASvL,UAAUuQ,IAAIuF,IAC5B7P,KAAKsF,SAASvL,UAAUxC,OAAO+iB,IAC/B/Z,EAAasB,QAAQ7B,KAAKsF,SAAUoK,GAAa,CAAE5P,mBAGfE,KAAKsF,UAAU,GACvD,CAEAsL,OACO5Q,KAAK2Q,WAIQpQ,EAAasB,QAAQ7B,KAAKsF,SAAUqK,IAExC1N,mBAIdjC,KAAK+Y,WAAWvC,aAChBxW,KAAKsF,SAASqV,OACd3a,KAAK2Q,UAAW,EAChB3Q,KAAKsF,SAASvL,UAAUuQ,IAAIiQ,IAC5Bva,KAAK6Y,UAAUjI,OAcf5Q,KAAK6F,eAZoB+U,KACvB5a,KAAKsF,SAASvL,UAAUxC,OAAOsY,GAAiB0K,IAChDva,KAAKsF,SAAS9B,gBAAgB,cAC9BxD,KAAKsF,SAAS9B,gBAAgB,QAEzBxD,KAAKuF,QAAQkV,SAChB,IAAI1D,IAAkBS,QAGxBjX,EAAasB,QAAQ7B,KAAKsF,SAAUsK,KAGA5P,KAAKsF,UAAU,IACvD,CAEAG,UACEzF,KAAK6Y,UAAUpT,UACfzF,KAAK+Y,WAAWvC,aAChBnR,MAAMI,SACR,CAGAqT,sBACE,MAUM3f,EAAY2H,QAAQd,KAAKuF,QAAQmQ,UAEvC,OAAO,IAAIL,GAAS,CAClBH,UAlJsB,qBAmJtB/b,YACA2M,YAAY,EACZsP,YAAapV,KAAKsF,SAAS3L,WAC3Bwb,cAAehc,EAjBKgc,KACU,WAA1BnV,KAAKuF,QAAQmQ,SAKjB1V,KAAK4Q,OAJHrQ,EAAasB,QAAQ7B,KAAKsF,SAAU8S,KAeK,MAE/C,CAEAY,uBACE,OAAO,IAAI9C,GAAU,CACnBD,YAAajW,KAAKsF,UAEtB,CAEAwH,qBACEvM,EAAac,GAAGrB,KAAKsF,SAAUkT,GAAuBpZ,IAtKvC,WAuKTA,EAAMxI,MAINoJ,KAAKuF,QAAQ2G,SACflM,KAAK4Q,OAIPrQ,EAAasB,QAAQ7B,KAAKsF,SAAU8S,MAExC,CAGA,sBAAOzc,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOkS,GAAU1U,oBAAoBhG,KAAMsE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBmE,IAAjBD,EAAKlE,IAAyBA,EAAO7C,WAAW,MAAmB,gBAAX6C,EAC1D,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,GAAQtE,KANb,CAOF,EACF,EAOFO,EAAac,GAAGpI,SAAUuS,GAzLG,+BAyLyC,SAAUpM,GAC9E,MAAMjC,EAASsJ,EAAekB,uBAAuB3H,MAMrD,GAJI,CAAC,IAAK,QAAQoB,SAASpB,KAAKiI,UAC9B7I,EAAMmD,iBAGJ3I,EAAWoG,MACb,OAGFO,EAAae,IAAInE,EAAQyS,GAAc,KAEjCzW,EAAU6G,OACZA,KAAKmT,UAKT,MAAMkH,EAAc5T,EAAeG,QAAQ4T,IACvCH,GAAeA,IAAgBld,GACjCud,GAAU3U,YAAYsU,GAAazJ,OAGxB8J,GAAU1U,oBAAoB7I,GACtCyL,OAAO5I,KACd,GAEAO,EAAac,GAAGzJ,OAAQ2T,GAAqB,KAC3C,IAAK,MAAM5T,KAAY8O,EAAetH,KAAKqb,IACzCE,GAAU1U,oBAAoBrO,GAAUkZ,SAI5CtQ,EAAac,GAAGzJ,OAAQygB,GAAc,KACpC,IAAK,MAAM1hB,KAAW8P,EAAetH,KAAK,gDACG,UAAvC7F,iBAAiB3C,GAASkkB,UAC5BH,GAAU1U,oBAAoBrP,GAASia,SAK7C/I,EAAqB6S,IAMrBvf,EAAmBuf,IC/QnB,MAEaI,GAAmB,CAE9B,IAAK,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,GAAI,GACJC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHhO,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDiO,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,IAIAC,GAAgB,IAAIpe,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eASIqe,GAAmB,0DAEnBC,GAAmBA,CAACC,EAAWC,KACnC,MAAMC,EAAgBF,EAAUG,SAAS5kB,cAEzC,OAAI0kB,EAAqB9b,SAAS+b,IAC5BL,GAAchmB,IAAIqmB,IACbrc,QAAQic,GAAiB9X,KAAKgY,EAAUI,YAO5CH,EAAqBrZ,OAAOyZ,GAAkBA,aAA0BtY,QAC5EuY,KAAKC,GAASA,EAAMvY,KAAKkY,KC9DxBjZ,GAAU,CACduZ,UAAW3C,GACX4C,QAAS,GACTC,WAAY,GACZC,MAAM,EACNC,UAAU,EACVC,WAAY,KACZC,SAAU,eAGN5Z,GAAc,CAClBsZ,UAAW,SACXC,QAAS,SACTC,WAAY,oBACZC,KAAM,UACNC,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNC,GAAqB,CACzBC,MAAO,iCACPtmB,SAAU,oBAOZ,MAAMumB,WAAwBja,EAC5BU,YAAYL,GACVe,QACArF,KAAKuF,QAAUvF,KAAKqE,WAAWC,EACjC,CAGA,kBAAWJ,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MA/CS,iBAgDX,CAGA2iB,aACE,OAAO/lB,OAAO8G,OAAOc,KAAKuF,QAAQmY,SAC/BpX,IAAIhC,GAAUtE,KAAKoe,yBAAyB9Z,IAC5CT,OAAO/C,QACZ,CAEAud,aACE,OAAOre,KAAKme,aAAanlB,OAAS,CACpC,CAEAslB,cAAcZ,GAGZ,OAFA1d,KAAKue,cAAcb,GACnB1d,KAAKuF,QAAQmY,QAAU,IAAK1d,KAAKuF,QAAQmY,WAAYA,GAC9C1d,IACT,CAEAwe,SACE,MAAMC,EAAkBxlB,SAAS0c,cAAc,OAC/C8I,EAAgBC,UAAY1e,KAAK2e,eAAe3e,KAAKuF,QAAQwY,UAE7D,IAAK,MAAOpmB,EAAUinB,KAASxmB,OAAO+I,QAAQnB,KAAKuF,QAAQmY,SACzD1d,KAAK6e,YAAYJ,EAAiBG,EAAMjnB,GAG1C,MAAMomB,EAAWU,EAAgB5X,SAAS,GACpC8W,EAAa3d,KAAKoe,yBAAyBpe,KAAKuF,QAAQoY,YAM9D,OAJIA,GACFI,EAAShkB,UAAUuQ,OAAOqT,EAAW5gB,MAAM,MAGtCghB,CACT,CAGAtZ,iBAAiBH,GACfe,MAAMZ,iBAAiBH,GACvBtE,KAAKue,cAAcja,EAAOoZ,QAC5B,CAEAa,cAAcO,GACZ,IAAK,MAAOnnB,EAAU+lB,KAAYtlB,OAAO+I,QAAQ2d,GAC/CzZ,MAAMZ,iBAAiB,CAAE9M,WAAUsmB,MAAOP,GAAWM,GAEzD,CAEAa,YAAYd,EAAUL,EAAS/lB,GAC7B,MAAMonB,EAAkBtY,EAAeG,QAAQjP,EAAUomB,GAEpDgB,KAILrB,EAAU1d,KAAKoe,yBAAyBV,IAOpC9kB,EAAU8kB,GACZ1d,KAAKgf,sBAAsBjmB,EAAW2kB,GAAUqB,GAI9C/e,KAAKuF,QAAQqY,KACfmB,EAAgBL,UAAY1e,KAAK2e,eAAejB,GAIlDqB,EAAgBE,YAAcvB,EAd5BqB,EAAgBxnB,SAepB,CAEAonB,eAAeG,GACb,OAAO9e,KAAKuF,QAAQsY,SD1DjB,SAAsBqB,EAAYzB,EAAW0B,GAClD,IAAKD,EAAWlmB,OACd,OAAOkmB,EAGT,GAAIC,GAAgD,mBAArBA,EAC7B,OAAOA,EAAiBD,GAG1B,MACME,GADY,IAAIxnB,OAAOynB,WACKC,gBAAgBJ,EAAY,aACxDzI,EAAW,GAAG/P,UAAU0Y,EAAgBrkB,KAAKqF,iBAAiB,MAEpE,IAAK,MAAMzJ,KAAW8f,EAAU,CAC9B,MAAM8I,EAAc5oB,EAAQymB,SAAS5kB,cAErC,IAAKJ,OAAOd,KAAKmmB,GAAWrc,SAASme,GAAc,CACjD5oB,EAAQY,SACR,QACF,CAEA,MAAMioB,EAAgB,GAAG9Y,UAAU/P,EAAQ+M,YACrC+b,EAAoB,GAAG/Y,OAAO+W,EAAU,MAAQ,GAAIA,EAAU8B,IAAgB,IAEpF,IAAK,MAAMtC,KAAauC,EACjBxC,GAAiBC,EAAWwC,IAC/B9oB,EAAQ6M,gBAAgByZ,EAAUG,SAGxC,CAEA,OAAOgC,EAAgBrkB,KAAK2jB,SAC9B,CC0BmCgB,CAAaZ,EAAK9e,KAAKuF,QAAQkY,UAAWzd,KAAKuF,QAAQuY,YAAcgB,CACtG,CAEAV,yBAAyBU,GACvB,OAAO7iB,EAAQ6iB,EAAK,MAACrW,EAAWzI,MAClC,CAEAgf,sBAAsBroB,EAASooB,GAC7B,GAAI/e,KAAKuF,QAAQqY,KAGf,OAFAmB,EAAgBL,UAAY,QAC5BK,EAAgBnJ,OAAOjf,GAIzBooB,EAAgBE,YAActoB,EAAQsoB,WACxC,ECvIF,MACMU,GAAwB,IAAIjhB,IAAI,CAAC,WAAY,YAAa,eAE1DkhB,GAAkB,OAElB/P,GAAkB,OAElBgQ,GAAyB,iBACzBC,GAAiB,SAEjBC,GAAmB,gBAEnBC,GAAgB,QAChBC,GAAgB,QAChBC,GAAgB,QAchBC,GAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,MAAOrlB,IAAU,OAAS,QAC1BslB,OAAQ,SACRC,KAAMvlB,IAAU,QAAU,QAGtBiJ,GAAU,CACduZ,UAAW3C,GACX2F,WAAW,EACXlO,SAAU,kBACVmO,WAAW,EACXC,YAAa,GACbC,MAAO,EACPC,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/CjD,MAAM,EACNnL,OAAQ,CAAC,EAAG,GACZwB,UAAW,MACXvB,aAAc,KACdmL,UAAU,EACVC,WAAY,KACZnmB,UAAU,EACVomB,SAAU,+GAIV+C,MAAO,GACPjf,QAAS,eAGLsC,GAAc,CAClBsZ,UAAW,SACXgD,UAAW,UACXlO,SAAU,mBACVmO,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPC,mBAAoB,QACpBjD,KAAM,UACNnL,OAAQ,0BACRwB,UAAW,oBACXvB,aAAc,yBACdmL,SAAU,UACVC,WAAY,kBACZnmB,SAAU,mBACVomB,SAAU,SACV+C,MAAO,4BACPjf,QAAS,UAOX,MAAMkf,WAAgB3b,EACpBT,YAAYhO,EAAS2N,GACnB,QAAsB,IAAXiP,EACT,MAAM,IAAIrO,UAAU,wEAGtBG,MAAM1O,EAAS2N,GAGftE,KAAKghB,YAAa,EAClBhhB,KAAKihB,SAAW,EAChBjhB,KAAKkhB,WAAa,KAClBlhB,KAAKmhB,eAAiB,GACtBnhB,KAAK6S,QAAU,KACf7S,KAAKohB,iBAAmB,KACxBphB,KAAKqhB,YAAc,KAGnBrhB,KAAKshB,IAAM,KAEXthB,KAAKuhB,gBAEAvhB,KAAKuF,QAAQ5N,UAChBqI,KAAKwhB,WAET,CAGA,kBAAWtd,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MAxHS,SAyHX,CAGAimB,SACEzhB,KAAKghB,YAAa,CACpB,CAEAU,UACE1hB,KAAKghB,YAAa,CACpB,CAEAW,gBACE3hB,KAAKghB,YAAchhB,KAAKghB,UAC1B,CAEApY,SACO5I,KAAKghB,aAINhhB,KAAK2Q,WACP3Q,KAAK4hB,SAIP5hB,KAAK6hB,SACP,CAEApc,UACE4I,aAAarO,KAAKihB,UAElB1gB,EAAaC,IAAIR,KAAKsF,SAAS7L,QAAQqmB,IAAiBC,GAAkB/f,KAAK8hB,mBAE3E9hB,KAAKsF,SAASnL,aAAa,2BAC7B6F,KAAKsF,SAAShC,aAAa,QAAStD,KAAKsF,SAASnL,aAAa,2BAGjE6F,KAAK+hB,iBACL1c,MAAMI,SACR,CAEAoL,OACE,GAAoC,SAAhC7Q,KAAKsF,SAAS6L,MAAMqB,QACtB,MAAM,IAAIpO,MAAM,uCAGlB,IAAMpE,KAAKgiB,mBAAoBhiB,KAAKghB,WAClC,OAGF,MAAM5G,EAAY7Z,EAAasB,QAAQ7B,KAAKsF,SAAUtF,KAAK2E,YAAYuB,UAxJxD,SA0JT+b,GADa7nB,EAAe4F,KAAKsF,WACLtF,KAAKsF,SAAS4c,cAAc7nB,iBAAiBL,SAASgG,KAAKsF,UAE7F,GAAI8U,EAAUnY,mBAAqBggB,EACjC,OAIFjiB,KAAK+hB,iBAEL,MAAMT,EAAMthB,KAAKmiB,iBAEjBniB,KAAKsF,SAAShC,aAAa,mBAAoBge,EAAInnB,aAAa,OAEhE,MAAMumB,UAAEA,GAAc1gB,KAAKuF,QAe3B,GAbKvF,KAAKsF,SAAS4c,cAAc7nB,gBAAgBL,SAASgG,KAAKshB,OAC7DZ,EAAU9K,OAAO0L,GACjB/gB,EAAasB,QAAQ7B,KAAKsF,SAAUtF,KAAK2E,YAAYuB,UAzKpC,cA4KnBlG,KAAK6S,QAAU7S,KAAKkT,cAAcoO,GAElCA,EAAIvnB,UAAUuQ,IAAIuF,IAMd,iBAAkB5W,SAASoB,gBAC7B,IAAK,MAAM1D,IAAW,GAAG+P,UAAUzN,SAAS8B,KAAK8L,UAC/CtG,EAAac,GAAG1K,EAAS,YAAa+D,GAc1CsF,KAAK6F,eAVYwL,KACf9Q,EAAasB,QAAQ7B,KAAKsF,SAAUtF,KAAK2E,YAAYuB,UA5LvC,WA8LU,IAApBlG,KAAKkhB,YACPlhB,KAAK4hB,SAGP5hB,KAAKkhB,YAAa,GAGUlhB,KAAKshB,IAAKthB,KAAKoP,cAC/C,CAEAwB,OACE,GAAK5Q,KAAK2Q,aAIQpQ,EAAasB,QAAQ7B,KAAKsF,SAAUtF,KAAK2E,YAAYuB,UAhNxD,SAiNDjE,iBAAd,CASA,GALYjC,KAAKmiB,iBACbpoB,UAAUxC,OAAOsY,IAIjB,iBAAkB5W,SAASoB,gBAC7B,IAAK,MAAM1D,IAAW,GAAG+P,UAAUzN,SAAS8B,KAAK8L,UAC/CtG,EAAaC,IAAI7J,EAAS,YAAa+D,GAI3CsF,KAAKmhB,eAAejB,KAAiB,EACrClgB,KAAKmhB,eAAelB,KAAiB,EACrCjgB,KAAKmhB,eAAenB,KAAiB,EACrChgB,KAAKkhB,WAAa,KAelBlhB,KAAK6F,eAbYwL,KACXrR,KAAKoiB,yBAIJpiB,KAAKkhB,YACRlhB,KAAK+hB,iBAGP/hB,KAAKsF,SAAS9B,gBAAgB,oBAC9BjD,EAAasB,QAAQ7B,KAAKsF,SAAUtF,KAAK2E,YAAYuB,UA9OtC,aAiPalG,KAAKshB,IAAKthB,KAAKoP,cA/B7C,CAgCF,CAEAkE,SACMtT,KAAK6S,SACP7S,KAAK6S,QAAQS,QAEjB,CAGA0O,iBACE,OAAOlhB,QAAQd,KAAKqiB,YACtB,CAEAF,iBAKE,OAJKniB,KAAKshB,MACRthB,KAAKshB,IAAMthB,KAAKsiB,kBAAkBtiB,KAAKqhB,aAAerhB,KAAKuiB,2BAGtDviB,KAAKshB,GACd,CAEAgB,kBAAkB5E,GAChB,MAAM4D,EAAMthB,KAAKwiB,oBAAoB9E,GAASc,SAG9C,IAAK8C,EACH,OAAO,KAGTA,EAAIvnB,UAAUxC,OAAOqoB,GAAiB/P,IAEtCyR,EAAIvnB,UAAUuQ,IAAI,MAAMtK,KAAK2E,YAAYnJ,aAEzC,MAAMinB,EpBpRKC,KACb,GACEA,GAAU5kB,KAAK6kB,MAjCH,IAiCS7kB,KAAK8kB,gBACnB3pB,SAAS4pB,eAAeH,IAEjC,OAAOA,GoB+QSI,CAAO9iB,KAAK2E,YAAYnJ,MAAMlD,WAQ5C,OANAgpB,EAAIhe,aAAa,KAAMmf,GAEnBziB,KAAKoP,eACPkS,EAAIvnB,UAAUuQ,IAAIsV,IAGb0B,CACT,CAEAyB,WAAWrF,GACT1d,KAAKqhB,YAAc3D,EACf1d,KAAK2Q,aACP3Q,KAAK+hB,iBACL/hB,KAAK6Q,OAET,CAEA2R,oBAAoB9E,GAalB,OAZI1d,KAAKohB,iBACPphB,KAAKohB,iBAAiB9C,cAAcZ,GAEpC1d,KAAKohB,iBAAmB,IAAIlD,GAAgB,IACvCle,KAAKuF,QAGRmY,UACAC,WAAY3d,KAAKoe,yBAAyBpe,KAAKuF,QAAQob,eAIpD3gB,KAAKohB,gBACd,CAEAmB,yBACE,MAAO,CACL1C,CAACA,IAAyB7f,KAAKqiB,YAEnC,CAEAA,YACE,OAAOriB,KAAKoe,yBAAyBpe,KAAKuF,QAAQub,QAAU9gB,KAAKsF,SAASnL,aAAa,yBACzF,CAGA6oB,6BAA6B5jB,GAC3B,OAAOY,KAAK2E,YAAYqB,oBAAoB5G,EAAMW,eAAgBC,KAAKijB,qBACzE,CAEA7T,cACE,OAAOpP,KAAKuF,QAAQkb,WAAczgB,KAAKshB,KAAOthB,KAAKshB,IAAIvnB,UAAUC,SAAS4lB,GAC5E,CAEAjP,WACE,OAAO3Q,KAAKshB,KAAOthB,KAAKshB,IAAIvnB,UAAUC,SAAS6V,GACjD,CAEAqD,cAAcoO,GACZ,MAAMrN,EAAYhY,EAAQ+D,KAAKuF,QAAQ0O,UAAW,CAACjU,KAAMshB,EAAKthB,KAAKsF,WAC7D4d,EAAa/C,GAAclM,EAAU9O,eAC3C,OAAOoO,EAAOG,aAAa1T,KAAKsF,SAAUgc,EAAKthB,KAAKyT,iBAAiByP,GACvE,CAEApP,aACE,MAAMrB,OAAEA,GAAWzS,KAAKuF,QAExB,MAAsB,iBAAXkN,EACFA,EAAO1V,MAAM,KAAKuJ,IAAI5D,GAAS9F,OAAO8R,SAAShM,EAAO,KAGzC,mBAAX+P,EACFsB,GAActB,EAAOsB,EAAY/T,KAAKsF,UAGxCmN,CACT,CAEA2L,yBAAyBU,GACvB,OAAO7iB,EAAQ6iB,EAAK,CAAC9e,KAAKsF,SAAUtF,KAAKsF,UAC3C,CAEAmO,iBAAiByP,GACf,MAAMlP,EAAwB,CAC5BC,UAAWiP,EACXhP,UAAW,CACT,CACE3Y,KAAM,OACN4Y,QAAS,CACP0M,mBAAoB7gB,KAAKuF,QAAQsb,qBAGrC,CACEtlB,KAAM,SACN4Y,QAAS,CACP1B,OAAQzS,KAAK8T,eAGjB,CACEvY,KAAM,kBACN4Y,QAAS,CACP5B,SAAUvS,KAAKuF,QAAQgN,WAG3B,CACEhX,KAAM,QACN4Y,QAAS,CACPxd,QAAS,IAAIqJ,KAAK2E,YAAYnJ,eAGlC,CACED,KAAM,kBACN6Y,SAAS,EACT+O,MAAO,aACPznB,GAAI8M,IAGFxI,KAAKmiB,iBAAiB7e,aAAa,wBAAyBkF,EAAK4a,MAAMnP,eAM/E,MAAO,IACFD,KACA/X,EAAQ+D,KAAKuF,QAAQmN,aAAc,MAACjK,EAAWuL,IAEtD,CAEAuN,gBACE,MAAM8B,EAAWrjB,KAAKuF,QAAQ1D,QAAQ9E,MAAM,KAE5C,IAAK,MAAM8E,KAAWwhB,EACpB,GAAgB,UAAZxhB,EACFtB,EAAac,GAAGrB,KAAKsF,SAAUtF,KAAK2E,YAAYuB,UArZpC,SAqZ4DlG,KAAKuF,QAAQ5N,SAAUyH,IAC7F,MAAMoV,EAAUxU,KAAKgjB,6BAA6B5jB,GAClDoV,EAAQ2M,eAAejB,MAAmB1L,EAAQ7D,YAAc6D,EAAQ2M,eAAejB,KACvF1L,EAAQ5L,gBAEL,GAjaU,WAiaN/G,EAA4B,CACrC,MAAMyhB,EAAUzhB,IAAYme,GAC1BhgB,KAAK2E,YAAYuB,UAzZF,cA0ZflG,KAAK2E,YAAYuB,UA5ZL,WA6ZRqd,EAAW1hB,IAAYme,GAC3BhgB,KAAK2E,YAAYuB,UA3ZF,cA4ZflG,KAAK2E,YAAYuB,UA9ZJ,YAgaf3F,EAAac,GAAGrB,KAAKsF,SAAUge,EAAStjB,KAAKuF,QAAQ5N,SAAUyH,IAC7D,MAAMoV,EAAUxU,KAAKgjB,6BAA6B5jB,GAClDoV,EAAQ2M,eAA8B,YAAf/hB,EAAMqB,KAAqBwf,GAAgBD,KAAiB,EACnFxL,EAAQqN,WAEVthB,EAAac,GAAGrB,KAAKsF,SAAUie,EAAUvjB,KAAKuF,QAAQ5N,SAAUyH,IAC9D,MAAMoV,EAAUxU,KAAKgjB,6BAA6B5jB,GAClDoV,EAAQ2M,eAA8B,aAAf/hB,EAAMqB,KAAsBwf,GAAgBD,IACjExL,EAAQlP,SAAStL,SAASoF,EAAMU,eAElC0U,EAAQoN,UAEZ,CAGF5hB,KAAK8hB,kBAAoB,KACnB9hB,KAAKsF,UACPtF,KAAK4Q,QAITrQ,EAAac,GAAGrB,KAAKsF,SAAS7L,QAAQqmB,IAAiBC,GAAkB/f,KAAK8hB,kBAChF,CAEAN,YACE,MAAMV,EAAQ9gB,KAAKsF,SAASnL,aAAa,SAEpC2mB,IAIA9gB,KAAKsF,SAASnL,aAAa,eAAkB6F,KAAKsF,SAAS2Z,YAAY5Y,QAC1ErG,KAAKsF,SAAShC,aAAa,aAAcwd,GAG3C9gB,KAAKsF,SAAShC,aAAa,yBAA0Bwd,GACrD9gB,KAAKsF,SAAS9B,gBAAgB,SAChC,CAEAqe,SACM7hB,KAAK2Q,YAAc3Q,KAAKkhB,WAC1BlhB,KAAKkhB,YAAa,GAIpBlhB,KAAKkhB,YAAa,EAElBlhB,KAAKwjB,YAAY,KACXxjB,KAAKkhB,YACPlhB,KAAK6Q,QAEN7Q,KAAKuF,QAAQqb,MAAM/P,MACxB,CAEA+Q,SACM5hB,KAAKoiB,yBAITpiB,KAAKkhB,YAAa,EAElBlhB,KAAKwjB,YAAY,KACVxjB,KAAKkhB,YACRlhB,KAAK4Q,QAEN5Q,KAAKuF,QAAQqb,MAAMhQ,MACxB,CAEA4S,YAAYtmB,EAASumB,GACnBpV,aAAarO,KAAKihB,UAClBjhB,KAAKihB,SAAW5jB,WAAWH,EAASumB,EACtC,CAEArB,uBACE,OAAOhqB,OAAO8G,OAAOc,KAAKmhB,gBAAgB/f,UAAS,EACrD,CAEAiD,WAAWC,GACT,MAAMof,EAAiBtgB,EAAYK,kBAAkBzD,KAAKsF,UAE1D,IAAK,MAAMqe,KAAiBvrB,OAAOd,KAAKosB,GAClC/D,GAAsB7oB,IAAI6sB,WACrBD,EAAeC,GAW1B,OAPArf,EAAS,IACJof,KACmB,iBAAXpf,GAAuBA,EAASA,EAAS,IAEtDA,EAAStE,KAAKuE,gBAAgBD,GAC9BA,EAAStE,KAAKwE,kBAAkBF,GAChCtE,KAAKyE,iBAAiBH,GACfA,CACT,CAEAE,kBAAkBF,GAkBhB,OAjBAA,EAAOoc,WAAiC,IAArBpc,EAAOoc,UAAsBznB,SAAS8B,KAAOhC,EAAWuL,EAAOoc,WAEtD,iBAAjBpc,EAAOsc,QAChBtc,EAAOsc,MAAQ,CACb/P,KAAMvM,EAAOsc,MACbhQ,KAAMtM,EAAOsc,QAIW,iBAAjBtc,EAAOwc,QAChBxc,EAAOwc,MAAQxc,EAAOwc,MAAMxoB,YAGA,iBAAnBgM,EAAOoZ,UAChBpZ,EAAOoZ,QAAUpZ,EAAOoZ,QAAQplB,YAG3BgM,CACT,CAEA2e,qBACE,MAAM3e,EAAS,GAEf,IAAK,MAAO1N,EAAK8L,KAAUtK,OAAO+I,QAAQnB,KAAKuF,SACzCvF,KAAK2E,YAAYT,QAAQtN,KAAS8L,IACpC4B,EAAO1N,GAAO8L,GAUlB,OANA4B,EAAO3M,UAAW,EAClB2M,EAAOzC,QAAU,SAKVyC,CACT,CAEAyd,iBACM/hB,KAAK6S,UACP7S,KAAK6S,QAAQQ,UACbrT,KAAK6S,QAAU,MAGb7S,KAAKshB,MACPthB,KAAKshB,IAAI/pB,SACTyI,KAAKshB,IAAM,KAEf,CAGA,sBAAO3lB,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOuY,GAAQ/a,oBAAoBhG,KAAMsE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBkE,EAAKlE,GACd,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IANL,CAOF,EACF,EAOFnJ,EAAmB4lB,ICxmBnB,MAEM6C,GAAiB,kBACjBC,GAAmB,gBAEnB3f,GAAU,IACX6c,GAAQ7c,QACXwZ,QAAS,GACTjL,OAAQ,CAAC,EAAG,GACZwB,UAAW,QACX8J,SAAU,8IAKVlc,QAAS,SAGLsC,GAAc,IACf4c,GAAQ5c,YACXuZ,QAAS,kCAOX,MAAMoG,WAAgB/C,GAEpB,kBAAW7c,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MAtCS,SAuCX,CAGAwmB,iBACE,OAAOhiB,KAAKqiB,aAAeriB,KAAK+jB,aAClC,CAGAxB,yBACE,MAAO,CACLqB,CAACA,IAAiB5jB,KAAKqiB,YACvBwB,CAACA,IAAmB7jB,KAAK+jB,cAE7B,CAEAA,cACE,OAAO/jB,KAAKoe,yBAAyBpe,KAAKuF,QAAQmY,QACpD,CAGA,sBAAO/hB,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOsb,GAAQ9d,oBAAoBhG,KAAMsE,GAE/C,GAAsB,iBAAXA,EAAX,CAIA,QAA4B,IAAjBkE,EAAKlE,GACd,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IANL,CAOF,EACF,EAOFnJ,EAAmB2oB,IC5EnB,MAEMpe,GAAY,gBAGZse,GAAiB,WAAWte,KAC5Bue,GAAc,QAAQve,KACtB6F,GAAsB,OAAO7F,cAG7BgG,GAAoB,SAGpBwY,GAAwB,SAExBC,GAAqB,YAGrBC,GAAsB,GAAGD,mBAA+CA,uBAIxEjgB,GAAU,CACduO,OAAQ,KACR4R,WAAY,eACZC,cAAc,EACdnnB,OAAQ,KACRonB,UAAW,CAAC,GAAK,GAAK,IAGlBpgB,GAAc,CAClBsO,OAAQ,gBACR4R,WAAY,SACZC,aAAc,UACdnnB,OAAQ,UACRonB,UAAW,SAOb,MAAMC,WAAkBpf,EACtBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAGftE,KAAKykB,aAAe,IAAIjuB,IACxBwJ,KAAK0kB,oBAAsB,IAAIluB,IAC/BwJ,KAAK2kB,aAA6D,YAA9CrrB,iBAAiB0G,KAAKsF,UAAU0U,UAA0B,KAAOha,KAAKsF,SAC1FtF,KAAK4kB,cAAgB,KACrB5kB,KAAK6kB,UAAY,KACjB7kB,KAAK8kB,oBAAsB,CACzBC,gBAAiB,EACjBC,gBAAiB,GAEnBhlB,KAAKilB,SACP,CAGA,kBAAW/gB,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MArES,WAsEX,CAGAypB,UACEjlB,KAAKklB,mCACLllB,KAAKmlB,2BAEDnlB,KAAK6kB,UACP7kB,KAAK6kB,UAAUO,aAEfplB,KAAK6kB,UAAY7kB,KAAKqlB,kBAGxB,IAAK,MAAMC,KAAWtlB,KAAK0kB,oBAAoBxlB,SAC7Cc,KAAK6kB,UAAUU,QAAQD,EAE3B,CAEA7f,UACEzF,KAAK6kB,UAAUO,aACf/f,MAAMI,SACR,CAGAjB,kBAAkBF,GAWhB,OATAA,EAAOnH,OAASpE,EAAWuL,EAAOnH,SAAWlE,SAAS8B,KAGtDuJ,EAAO+f,WAAa/f,EAAOmO,OAAS,GAAGnO,EAAOmO,oBAAsBnO,EAAO+f,WAE3C,iBAArB/f,EAAOigB,YAChBjgB,EAAOigB,UAAYjgB,EAAOigB,UAAUxnB,MAAM,KAAKuJ,IAAI5D,GAAS9F,OAAOC,WAAW6F,KAGzE4B,CACT,CAEA6gB,2BACOnlB,KAAKuF,QAAQ+e,eAKlB/jB,EAAaC,IAAIR,KAAKuF,QAAQpI,OAAQ8mB,IAEtC1jB,EAAac,GAAGrB,KAAKuF,QAAQpI,OAAQ8mB,GAAaC,GAAuB9kB,IACvE,MAAMomB,EAAoBxlB,KAAK0kB,oBAAoB1tB,IAAIoI,EAAMjC,OAAOsoB,MACpE,GAAID,EAAmB,CACrBpmB,EAAMmD,iBACN,MAAM/H,EAAOwF,KAAK2kB,cAAgB/sB,OAC5B8tB,EAASF,EAAkBG,UAAY3lB,KAAKsF,SAASqgB,UAC3D,GAAInrB,EAAKorB,SAEP,YADAprB,EAAKorB,SAAS,CAAEC,IAAKH,EAAQI,SAAU,WAKzCtrB,EAAK8e,UAAYoM,CACnB,IAEJ,CAEAL,kBACE,MAAMlR,EAAU,CACd3Z,KAAMwF,KAAK2kB,aACXJ,UAAWvkB,KAAKuF,QAAQgf,UACxBF,WAAYrkB,KAAKuF,QAAQ8e,YAG3B,OAAO,IAAI0B,qBAAqB5kB,GAAWnB,KAAKgmB,kBAAkB7kB,GAAUgT,EAC9E,CAGA6R,kBAAkB7kB,GAChB,MAAM8kB,EAAgBhI,GAASje,KAAKykB,aAAaztB,IAAI,IAAIinB,EAAM9gB,OAAOlF,MAChEoe,EAAW4H,IACfje,KAAK8kB,oBAAoBC,gBAAkB9G,EAAM9gB,OAAOwoB,UACxD3lB,KAAKkmB,SAASD,EAAchI,KAGxB+G,GAAmBhlB,KAAK2kB,cAAgB1rB,SAASoB,iBAAiBif,UAClE6M,EAAkBnB,GAAmBhlB,KAAK8kB,oBAAoBE,gBACpEhlB,KAAK8kB,oBAAoBE,gBAAkBA,EAE3C,IAAK,MAAM/G,KAAS9c,EAAS,CAC3B,IAAK8c,EAAMmI,eAAgB,CACzBpmB,KAAK4kB,cAAgB,KACrB5kB,KAAKqmB,kBAAkBJ,EAAchI,IAErC,QACF,CAEA,MAAMqI,EAA2BrI,EAAM9gB,OAAOwoB,WAAa3lB,KAAK8kB,oBAAoBC,gBAEpF,GAAIoB,GAAmBG,GAGrB,GAFAjQ,EAAS4H,IAEJ+G,EACH,YAOCmB,GAAoBG,GACvBjQ,EAAS4H,EAEb,CACF,CAEAiH,mCACEllB,KAAKykB,aAAe,IAAIjuB,IACxBwJ,KAAK0kB,oBAAsB,IAAIluB,IAE/B,MAAM+vB,EAAc9f,EAAetH,KAAK+kB,GAAuBlkB,KAAKuF,QAAQpI,QAE5E,IAAK,MAAMqpB,KAAUD,EAAa,CAEhC,IAAKC,EAAOf,MAAQ7rB,EAAW4sB,GAC7B,SAGF,MAAMhB,EAAoB/e,EAAeG,QAAQ6f,UAAUD,EAAOf,MAAOzlB,KAAKsF,UAG1EnM,EAAUqsB,KACZxlB,KAAKykB,aAAa/tB,IAAI+vB,UAAUD,EAAOf,MAAOe,GAC9CxmB,KAAK0kB,oBAAoBhuB,IAAI8vB,EAAOf,KAAMD,GAE9C,CACF,CAEAU,SAAS/oB,GACH6C,KAAK4kB,gBAAkBznB,IAI3B6C,KAAKqmB,kBAAkBrmB,KAAKuF,QAAQpI,QACpC6C,KAAK4kB,cAAgBznB,EACrBA,EAAOpD,UAAUuQ,IAAIoB,IACrB1L,KAAK0mB,iBAAiBvpB,GAEtBoD,EAAasB,QAAQ7B,KAAKsF,SAAU0e,GAAgB,CAAElkB,cAAe3C,IACvE,CAEAupB,iBAAiBvpB,GAEf,GAAIA,EAAOpD,UAAUC,SAlNQ,iBAmN3ByM,EAAeG,QAxMY,mBAwMsBzJ,EAAO1D,QAzMpC,cA0MjBM,UAAUuQ,IAAIoB,SAInB,IAAK,MAAMib,KAAalgB,EAAeO,QAAQ7J,EAnNnB,qBAsN1B,IAAK,MAAMypB,KAAQngB,EAAeS,KAAKyf,EAAWvC,IAChDwC,EAAK7sB,UAAUuQ,IAAIoB,GAGzB,CAEA2a,kBAAkBpW,GAChBA,EAAOlW,UAAUxC,OAAOmU,IAExB,MAAMmb,EAAcpgB,EAAetH,KAAK,GAAG+kB,MAAyBxY,KAAqBuE,GACzF,IAAK,MAAM6W,KAAQD,EACjBC,EAAK/sB,UAAUxC,OAAOmU,GAE1B,CAGA,sBAAO/P,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOgc,GAAUxe,oBAAoBhG,KAAMsE,GAEjD,GAAsB,iBAAXA,EAAX,CAIA,QAAqBmE,IAAjBD,EAAKlE,IAAyBA,EAAO7C,WAAW,MAAmB,gBAAX6C,EAC1D,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IANL,CAOF,EACF,EAOF/D,EAAac,GAAGzJ,OAAQ2T,GAAqB,KAC3C,IAAK,MAAMwb,KAAOtgB,EAAetH,KA9PT,0BA+PtBqlB,GAAUxe,oBAAoB+gB,KAQlC5rB,EAAmBqpB,ICrRnB,MAEM9e,GAAY,UAEZiK,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KACtB8F,GAAuB,QAAQ9F,KAC/ByF,GAAgB,UAAUzF,KAC1B6F,GAAsB,OAAO7F,KAE7BiF,GAAiB,YACjBC,GAAkB,aAClB8G,GAAe,UACfC,GAAiB,YACjBqV,GAAW,OACXC,GAAU,MAEVvb,GAAoB,SACpBkU,GAAkB,OAClB/P,GAAkB,OAGlBqX,GAA2B,mBAE3BC,GAA+B,QAAQD,MAKvCxe,GAAuB,2EACvB0e,GAAsB,YAFOD,uBAAiDA,mBAA6CA,OAE/Eze,KAE5C2e,GAA8B,IAAI3b,8BAA6CA,+BAA8CA,4BAMnI,MAAM4b,WAAYliB,EAChBT,YAAYhO,GACV0O,MAAM1O,GACNqJ,KAAK8S,QAAU9S,KAAKsF,SAAS7L,QAfN,uCAiBlBuG,KAAK8S,UAOV9S,KAAKunB,sBAAsBvnB,KAAK8S,QAAS9S,KAAKwnB,gBAE9CjnB,EAAac,GAAGrB,KAAKsF,SAAU6F,GAAe/L,GAASY,KAAK+N,SAAS3O,IACvE,CAGA,eAAW5D,GACT,MA3DS,KA4DX,CAGAqV,OACE,MAAM4W,EAAYznB,KAAKsF,SACvB,GAAItF,KAAK0nB,cAAcD,GACrB,OAIF,MAAME,EAAS3nB,KAAK4nB,iBAEdC,EAAYF,EAChBpnB,EAAasB,QAAQ8lB,EAAQhY,GAAY,CAAE7P,cAAe2nB,IAC1D,KAEgBlnB,EAAasB,QAAQ4lB,EAAWhY,GAAY,CAAE3P,cAAe6nB,IAEjE1lB,kBAAqB4lB,GAAaA,EAAU5lB,mBAI1DjC,KAAK8nB,YAAYH,EAAQF,GACzBznB,KAAK+nB,UAAUN,EAAWE,GAC5B,CAGAI,UAAUpxB,EAASqxB,GACZrxB,IAILA,EAAQoD,UAAUuQ,IAAIoB,IAEtB1L,KAAK+nB,UAAUthB,EAAekB,uBAAuBhR,IAgBrDqJ,KAAK6F,eAdYwL,KACsB,QAAjC1a,EAAQwD,aAAa,SAKzBxD,EAAQ6M,gBAAgB,YACxB7M,EAAQ2M,aAAa,iBAAiB,GACtCtD,KAAKioB,gBAAgBtxB,GAAS,GAC9B4J,EAAasB,QAAQlL,EAAS+Y,GAAa,CACzC5P,cAAekoB,KARfrxB,EAAQoD,UAAUuQ,IAAIuF,KAYIlZ,EAASA,EAAQoD,UAAUC,SAAS4lB,KACpE,CAEAkI,YAAYnxB,EAASqxB,GACdrxB,IAILA,EAAQoD,UAAUxC,OAAOmU,IACzB/U,EAAQgkB,OAER3a,KAAK8nB,YAAYrhB,EAAekB,uBAAuBhR,IAcvDqJ,KAAK6F,eAZYwL,KACsB,QAAjC1a,EAAQwD,aAAa,SAKzBxD,EAAQ2M,aAAa,iBAAiB,GACtC3M,EAAQ2M,aAAa,WAAY,MACjCtD,KAAKioB,gBAAgBtxB,GAAS,GAC9B4J,EAAasB,QAAQlL,EAASiZ,GAAc,CAAE9P,cAAekoB,KAP3DrxB,EAAQoD,UAAUxC,OAAOsY,KAUClZ,EAASA,EAAQoD,UAAUC,SAAS4lB,KACpE,CAEA7R,SAAS3O,GACP,IAAM,CAACuL,GAAgBC,GAAiB8G,GAAcC,GAAgBqV,GAAUC,IAAS7lB,SAAShC,EAAMxI,KACtG,OAGFwI,EAAM4V,kBACN5V,EAAMmD,iBAEN,MAAMsE,EAAW7G,KAAKwnB,eAAe3jB,OAAOlN,IAAYiD,EAAWjD,IACnE,IAAIuxB,EAEJ,GAAI,CAAClB,GAAUC,IAAS7lB,SAAShC,EAAMxI,KACrCsxB,EAAoBrhB,EAASzH,EAAMxI,MAAQowB,GAAW,EAAIngB,EAAS7N,OAAS,OACvE,CACL,MAAM2V,EAAS,CAAC/D,GAAiB+G,IAAgBvQ,SAAShC,EAAMxI,KAChEsxB,EAAoB5qB,EAAqBuJ,EAAUzH,EAAMjC,OAAQwR,GAAQ,EAC3E,CAEIuZ,IACFA,EAAkB/U,MAAM,CAAEgV,eAAe,IACzCb,GAAIthB,oBAAoBkiB,GAAmBrX,OAE/C,CAEA2W,eACE,OAAO/gB,EAAetH,KAAKioB,GAAqBpnB,KAAK8S,QACvD,CAEA8U,iBACE,OAAO5nB,KAAKwnB,eAAeroB,KAAK2H,GAAS9G,KAAK0nB,cAAc5gB,KAAW,IACzE,CAEAygB,sBAAsBtX,EAAQpJ,GAC5B7G,KAAKooB,yBAAyBnY,EAAQ,OAAQ,WAE9C,IAAK,MAAMnJ,KAASD,EAClB7G,KAAKqoB,6BAA6BvhB,EAEtC,CAEAuhB,6BAA6BvhB,GAC3BA,EAAQ9G,KAAKsoB,iBAAiBxhB,GAC9B,MAAMyhB,EAAWvoB,KAAK0nB,cAAc5gB,GAC9B0hB,EAAYxoB,KAAKyoB,iBAAiB3hB,GACxCA,EAAMxD,aAAa,gBAAiBilB,GAEhCC,IAAc1hB,GAChB9G,KAAKooB,yBAAyBI,EAAW,OAAQ,gBAG9CD,GACHzhB,EAAMxD,aAAa,WAAY,MAGjCtD,KAAKooB,yBAAyBthB,EAAO,OAAQ,OAG7C9G,KAAK0oB,mCAAmC5hB,EAC1C,CAEA4hB,mCAAmC5hB,GACjC,MAAM3J,EAASsJ,EAAekB,uBAAuBb,GAEhD3J,IAIL6C,KAAKooB,yBAAyBjrB,EAAQ,OAAQ,YAE1C2J,EAAM7O,IACR+H,KAAKooB,yBAAyBjrB,EAAQ,kBAAmB,GAAG2J,EAAM7O,MAEtE,CAEAgwB,gBAAgBtxB,EAASgyB,GACvB,MAAMH,EAAYxoB,KAAKyoB,iBAAiB9xB,GACxC,IAAK6xB,EAAUzuB,UAAUC,SAhMN,YAiMjB,OAGF,MAAM4O,EAASA,CAACjR,EAAUud,KACxB,MAAMve,EAAU8P,EAAeG,QAAQjP,EAAU6wB,GAC7C7xB,GACFA,EAAQoD,UAAU6O,OAAOsM,EAAWyT,IAIxC/f,EAAOse,GAA0Bxb,IACjC9C,EAzM2B,iBAyMIiH,IAC/B2Y,EAAUllB,aAAa,gBAAiBqlB,EAC1C,CAEAP,yBAAyBzxB,EAASsmB,EAAWva,GACtC/L,EAAQuD,aAAa+iB,IACxBtmB,EAAQ2M,aAAa2Z,EAAWva,EAEpC,CAEAglB,cAAcpX,GACZ,OAAOA,EAAKvW,UAAUC,SAAS0R,GACjC,CAGA4c,iBAAiBhY,GACf,OAAOA,EAAKvJ,QAAQqgB,IAAuB9W,EAAO7J,EAAeG,QAAQwgB,GAAqB9W,EAChG,CAGAmY,iBAAiBnY,GACf,OAAOA,EAAK7W,QA1NO,gCA0NoB6W,CACzC,CAGA,sBAAO3U,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAO8e,GAAIthB,oBAAoBhG,MAErC,GAAsB,iBAAXsE,EAAX,CAIA,QAAqBmE,IAAjBD,EAAKlE,IAAyBA,EAAO7C,WAAW,MAAmB,gBAAX6C,EAC1D,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,IANL,CAOF,EACF,EAOF/D,EAAac,GAAGpI,SAAUuS,GAAsB9C,GAAsB,SAAUtJ,GAC1E,CAAC,IAAK,QAAQgC,SAASpB,KAAKiI,UAC9B7I,EAAMmD,iBAGJ3I,EAAWoG,OAIfsnB,GAAIthB,oBAAoBhG,MAAM6Q,MAChC,GAKAtQ,EAAac,GAAGzJ,OAAQ2T,GAAqB,KAC3C,IAAK,MAAM5U,KAAW8P,EAAetH,KAAKkoB,IACxCC,GAAIthB,oBAAoBrP,KAO5BwE,EAAmBmsB,ICxSnB,MAEM5hB,GAAY,YAEZkjB,GAAkB,YAAYljB,KAC9BmjB,GAAiB,WAAWnjB,KAC5BmQ,GAAgB,UAAUnQ,KAC1BojB,GAAiB,WAAWpjB,KAC5BiK,GAAa,OAAOjK,KACpBkK,GAAe,SAASlK,KACxB+J,GAAa,OAAO/J,KACpBgK,GAAc,QAAQhK,KAGtBqjB,GAAkB,OAClBlZ,GAAkB,OAClByK,GAAqB,UAErBnW,GAAc,CAClBsc,UAAW,UACXuI,SAAU,UACVpI,MAAO,UAGH1c,GAAU,CACduc,WAAW,EACXuI,UAAU,EACVpI,MAAO,KAOT,MAAMqI,WAAc7jB,EAClBT,YAAYhO,EAAS2N,GACnBe,MAAM1O,EAAS2N,GAEftE,KAAKihB,SAAW,KAChBjhB,KAAKkpB,sBAAuB,EAC5BlpB,KAAKmpB,yBAA0B,EAC/BnpB,KAAKuhB,eACP,CAGA,kBAAWrd,GACT,OAAOA,EACT,CAEA,sBAAWC,GACT,OAAOA,EACT,CAEA,eAAW3I,GACT,MAtDS,OAuDX,CAGAqV,OACoBtQ,EAAasB,QAAQ7B,KAAKsF,SAAUmK,IAExCxN,mBAIdjC,KAAKopB,gBAEDppB,KAAKuF,QAAQkb,WACfzgB,KAAKsF,SAASvL,UAAUuQ,IAvDN,QAiEpBtK,KAAKsF,SAASvL,UAAUxC,OAAOwxB,IAC/BpuB,EAAOqF,KAAKsF,UACZtF,KAAKsF,SAASvL,UAAUuQ,IAAIuF,GAAiByK,IAE7Cta,KAAK6F,eAXYwL,KACfrR,KAAKsF,SAASvL,UAAUxC,OAAO+iB,IAC/B/Z,EAAasB,QAAQ7B,KAAKsF,SAAUoK,IAEpC1P,KAAKqpB,sBAOuBrpB,KAAKsF,SAAUtF,KAAKuF,QAAQkb,WAC5D,CAEA7P,OACO5Q,KAAKspB,YAIQ/oB,EAAasB,QAAQ7B,KAAKsF,SAAUqK,IAExC1N,mBAUdjC,KAAKsF,SAASvL,UAAUuQ,IAAIgQ,IAC5Bta,KAAK6F,eAPYwL,KACfrR,KAAKsF,SAASvL,UAAUuQ,IAAIye,IAC5B/oB,KAAKsF,SAASvL,UAAUxC,OAAO+iB,GAAoBzK,IACnDtP,EAAasB,QAAQ7B,KAAKsF,SAAUsK,KAIR5P,KAAKsF,SAAUtF,KAAKuF,QAAQkb,YAC5D,CAEAhb,UACEzF,KAAKopB,gBAEDppB,KAAKspB,WACPtpB,KAAKsF,SAASvL,UAAUxC,OAAOsY,IAGjCxK,MAAMI,SACR,CAEA6jB,UACE,OAAOtpB,KAAKsF,SAASvL,UAAUC,SAAS6V,GAC1C,CAGAwZ,qBACOrpB,KAAKuF,QAAQyjB,WAIdhpB,KAAKkpB,sBAAwBlpB,KAAKmpB,0BAItCnpB,KAAKihB,SAAW5jB,WAAW,KACzB2C,KAAK4Q,QACJ5Q,KAAKuF,QAAQqb,QAClB,CAEA2I,eAAenqB,EAAOoqB,GACpB,OAAQpqB,EAAMqB,MACZ,IAAK,YACL,IAAK,WACHT,KAAKkpB,qBAAuBM,EAC5B,MAGF,IAAK,UACL,IAAK,WACHxpB,KAAKmpB,wBAA0BK,EASnC,GAAIA,EAEF,YADAxpB,KAAKopB,gBAIP,MAAMxa,EAAcxP,EAAMU,cACtBE,KAAKsF,WAAasJ,GAAe5O,KAAKsF,SAAStL,SAAS4U,IAI5D5O,KAAKqpB,oBACP,CAEA9H,gBACEhhB,EAAac,GAAGrB,KAAKsF,SAAUsjB,GAAiBxpB,GAASY,KAAKupB,eAAenqB,GAAO,IACpFmB,EAAac,GAAGrB,KAAKsF,SAAUujB,GAAgBzpB,GAASY,KAAKupB,eAAenqB,GAAO,IACnFmB,EAAac,GAAGrB,KAAKsF,SAAUuQ,GAAezW,GAASY,KAAKupB,eAAenqB,GAAO,IAClFmB,EAAac,GAAGrB,KAAKsF,SAAUwjB,GAAgB1pB,GAASY,KAAKupB,eAAenqB,GAAO,GACrF,CAEAgqB,gBACE/a,aAAarO,KAAKihB,UAClBjhB,KAAKihB,SAAW,IAClB,CAGA,sBAAOtlB,CAAgB2I,GACrB,OAAOtE,KAAKuI,KAAK,WACf,MAAMC,EAAOygB,GAAMjjB,oBAAoBhG,KAAMsE,GAE7C,GAAsB,iBAAXA,EAAqB,CAC9B,QAA4B,IAAjBkE,EAAKlE,GACd,MAAM,IAAIY,UAAU,oBAAoBZ,MAG1CkE,EAAKlE,GAAQtE,KACf,CACF,EACF,E,OAOF6H,EAAqBohB,IAMrB9tB,EAAmB8tB,ICzMJ,CACb7gB,QACAO,SACA4D,YACA2D,YACA0C,YACA+F,SACA+B,aACAoJ,WACAU,aACA8C,OACA2B,SACAlI,W", "ignoreList": []}