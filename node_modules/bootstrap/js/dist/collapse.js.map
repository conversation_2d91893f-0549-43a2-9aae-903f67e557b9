{"version": 3, "file": "collapse.js", "sources": ["../src/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  // Private\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_SHOW", "CLASS_NAME_COLLAPSE", "CLASS_NAME_COLLAPSING", "CLASS_NAME_COLLAPSED", "CLASS_NAME_DEEPER_CHILDREN", "CLASS_NAME_HORIZONTAL", "WIDTH", "HEIGHT", "SELECTOR_ACTIVES", "SELECTOR_DATA_TOGGLE", "<PERSON><PERSON><PERSON>", "parent", "toggle", "DefaultType", "Collapse", "BaseComponent", "constructor", "element", "config", "_isTransitioning", "_triggerArray", "toggleList", "SelectorEngine", "find", "elem", "selector", "getSelectorFromElement", "filterElement", "filter", "foundElement", "_element", "length", "push", "_initializeC<PERSON><PERSON>n", "_config", "_addAriaAndCollapsedClass", "_isShown", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "map", "getOrCreateInstance", "startEvent", "EventHandler", "trigger", "defaultPrevented", "activeInstance", "dimension", "_getDimension", "classList", "remove", "add", "style", "complete", "capitalizedDimension", "toUpperCase", "slice", "scrollSize", "_queueCallback", "getBoundingClientRect", "reflow", "getElementFromSelector", "contains", "_configAfterMerge", "Boolean", "getElement", "children", "selected", "includes", "trigger<PERSON><PERSON>y", "isOpen", "setAttribute", "jQueryInterface", "test", "each", "data", "TypeError", "on", "document", "event", "target", "tagName", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "getMultipleElementsFromSelector", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAWA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAU;EACvB,MAAMC,QAAQ,GAAG,aAAa;EAC9B,MAAMC,SAAS,GAAG,CAAA,CAAA,EAAID,QAAQ,CAAA,CAAE;EAChC,MAAME,YAAY,GAAG,WAAW;EAEhC,MAAMC,UAAU,GAAG,CAAA,IAAA,EAAOF,SAAS,CAAA,CAAE;EACrC,MAAMG,WAAW,GAAG,CAAA,KAAA,EAAQH,SAAS,CAAA,CAAE;EACvC,MAAMI,UAAU,GAAG,CAAA,IAAA,EAAOJ,SAAS,CAAA,CAAE;EACrC,MAAMK,YAAY,GAAG,CAAA,MAAA,EAASL,SAAS,CAAA,CAAE;EACzC,MAAMM,oBAAoB,GAAG,CAAA,KAAA,EAAQN,SAAS,CAAA,EAAGC,YAAY,CAAA,CAAE;EAE/D,MAAMM,eAAe,GAAG,MAAM;EAC9B,MAAMC,mBAAmB,GAAG,UAAU;EACtC,MAAMC,qBAAqB,GAAG,YAAY;EAC1C,MAAMC,oBAAoB,GAAG,WAAW;EACxC,MAAMC,0BAA0B,GAAG,CAAA,QAAA,EAAWH,mBAAmB,CAAA,EAAA,EAAKA,mBAAmB,CAAA,CAAE;EAC3F,MAAMI,qBAAqB,GAAG,qBAAqB;EAEnD,MAAMC,KAAK,GAAG,OAAO;EACrB,MAAMC,MAAM,GAAG,QAAQ;EAEvB,MAAMC,gBAAgB,GAAG,sCAAsC;EAC/D,MAAMC,oBAAoB,GAAG,6BAA6B;EAE1D,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IAAI;EACZC,EAAAA,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBF,EAAAA,MAAM,EAAE,gBAAgB;EACxBC,EAAAA,MAAM,EAAE;EACV,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,QAAQ,SAASC,aAAa,CAAC;EACnCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC;MAEtB,IAAI,CAACC,gBAAgB,GAAG,KAAK;MAC7B,IAAI,CAACC,aAAa,GAAG,EAAE;EAEvB,IAAA,MAAMC,UAAU,GAAGC,cAAc,CAACC,IAAI,CAACd,oBAAoB,CAAC;EAE5D,IAAA,KAAK,MAAMe,IAAI,IAAIH,UAAU,EAAE;EAC7B,MAAA,MAAMI,QAAQ,GAAGH,cAAc,CAACI,sBAAsB,CAACF,IAAI,CAAC;EAC5D,MAAA,MAAMG,aAAa,GAAGL,cAAc,CAACC,IAAI,CAACE,QAAQ,CAAC,CAChDG,MAAM,CAACC,YAAY,IAAIA,YAAY,KAAK,IAAI,CAACC,QAAQ,CAAC;EAEzD,MAAA,IAAIL,QAAQ,KAAK,IAAI,IAAIE,aAAa,CAACI,MAAM,EAAE;EAC7C,QAAA,IAAI,CAACX,aAAa,CAACY,IAAI,CAACR,IAAI,CAAC;EAC/B,MAAA;EACF,IAAA;MAEA,IAAI,CAACS,mBAAmB,EAAE;EAE1B,IAAA,IAAI,CAAC,IAAI,CAACC,OAAO,CAACvB,MAAM,EAAE;EACxB,MAAA,IAAI,CAACwB,yBAAyB,CAAC,IAAI,CAACf,aAAa,EAAE,IAAI,CAACgB,QAAQ,EAAE,CAAC;EACrE,IAAA;EAEA,IAAA,IAAI,IAAI,CAACF,OAAO,CAACtB,MAAM,EAAE;QACvB,IAAI,CAACA,MAAM,EAAE;EACf,IAAA;EACF,EAAA;;EAEA;IACA,WAAWF,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB,EAAA;IAEA,WAAWG,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB,EAAA;IAEA,WAAWtB,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb,EAAA;;EAEA;EACAqB,EAAAA,MAAMA,GAAG;EACP,IAAA,IAAI,IAAI,CAACwB,QAAQ,EAAE,EAAE;QACnB,IAAI,CAACC,IAAI,EAAE;EACb,IAAA,CAAC,MAAM;QACL,IAAI,CAACC,IAAI,EAAE;EACb,IAAA;EACF,EAAA;EAEAA,EAAAA,IAAIA,GAAG;MACL,IAAI,IAAI,CAACnB,gBAAgB,IAAI,IAAI,CAACiB,QAAQ,EAAE,EAAE;EAC5C,MAAA;EACF,IAAA;MAEA,IAAIG,cAAc,GAAG,EAAE;;EAEvB;EACA,IAAA,IAAI,IAAI,CAACL,OAAO,CAACvB,MAAM,EAAE;EACvB4B,MAAAA,cAAc,GAAG,IAAI,CAACC,sBAAsB,CAAChC,gBAAgB,CAAC,CAC3DoB,MAAM,CAACX,OAAO,IAAIA,OAAO,KAAK,IAAI,CAACa,QAAQ,CAAC,CAC5CW,GAAG,CAACxB,OAAO,IAAIH,QAAQ,CAAC4B,mBAAmB,CAACzB,OAAO,EAAE;EAAEL,QAAAA,MAAM,EAAE;EAAM,OAAC,CAAC,CAAC;EAC7E,IAAA;MAEA,IAAI2B,cAAc,CAACR,MAAM,IAAIQ,cAAc,CAAC,CAAC,CAAC,CAACpB,gBAAgB,EAAE;EAC/D,MAAA;EACF,IAAA;MAEA,MAAMwB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACf,QAAQ,EAAEnC,UAAU,CAAC;MAClE,IAAIgD,UAAU,CAACG,gBAAgB,EAAE;EAC/B,MAAA;EACF,IAAA;EAEA,IAAA,KAAK,MAAMC,cAAc,IAAIR,cAAc,EAAE;QAC3CQ,cAAc,CAACV,IAAI,EAAE;EACvB,IAAA;EAEA,IAAA,MAAMW,SAAS,GAAG,IAAI,CAACC,aAAa,EAAE;MAEtC,IAAI,CAACnB,QAAQ,CAACoB,SAAS,CAACC,MAAM,CAAClD,mBAAmB,CAAC;MACnD,IAAI,CAAC6B,QAAQ,CAACoB,SAAS,CAACE,GAAG,CAAClD,qBAAqB,CAAC;MAElD,IAAI,CAAC4B,QAAQ,CAACuB,KAAK,CAACL,SAAS,CAAC,GAAG,CAAC;MAElC,IAAI,CAACb,yBAAyB,CAAC,IAAI,CAACf,aAAa,EAAE,IAAI,CAAC;MACxD,IAAI,CAACD,gBAAgB,GAAG,IAAI;MAE5B,MAAMmC,QAAQ,GAAGA,MAAM;QACrB,IAAI,CAACnC,gBAAgB,GAAG,KAAK;QAE7B,IAAI,CAACW,QAAQ,CAACoB,SAAS,CAACC,MAAM,CAACjD,qBAAqB,CAAC;QACrD,IAAI,CAAC4B,QAAQ,CAACoB,SAAS,CAACE,GAAG,CAACnD,mBAAmB,EAAED,eAAe,CAAC;QAEjE,IAAI,CAAC8B,QAAQ,CAACuB,KAAK,CAACL,SAAS,CAAC,GAAG,EAAE;QAEnCJ,YAAY,CAACC,OAAO,CAAC,IAAI,CAACf,QAAQ,EAAElC,WAAW,CAAC;MAClD,CAAC;EAED,IAAA,MAAM2D,oBAAoB,GAAGP,SAAS,CAAC,CAAC,CAAC,CAACQ,WAAW,EAAE,GAAGR,SAAS,CAACS,KAAK,CAAC,CAAC,CAAC;EAC5E,IAAA,MAAMC,UAAU,GAAG,CAAA,MAAA,EAASH,oBAAoB,CAAA,CAAE;MAElD,IAAI,CAACI,cAAc,CAACL,QAAQ,EAAE,IAAI,CAACxB,QAAQ,EAAE,IAAI,CAAC;EAClD,IAAA,IAAI,CAACA,QAAQ,CAACuB,KAAK,CAACL,SAAS,CAAC,GAAG,CAAA,EAAG,IAAI,CAAClB,QAAQ,CAAC4B,UAAU,CAAC,CAAA,EAAA,CAAI;EACnE,EAAA;EAEArB,EAAAA,IAAIA,GAAG;MACL,IAAI,IAAI,CAAClB,gBAAgB,IAAI,CAAC,IAAI,CAACiB,QAAQ,EAAE,EAAE;EAC7C,MAAA;EACF,IAAA;MAEA,MAAMO,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACf,QAAQ,EAAEjC,UAAU,CAAC;MAClE,IAAI8C,UAAU,CAACG,gBAAgB,EAAE;EAC/B,MAAA;EACF,IAAA;EAEA,IAAA,MAAME,SAAS,GAAG,IAAI,CAACC,aAAa,EAAE;EAEtC,IAAA,IAAI,CAACnB,QAAQ,CAACuB,KAAK,CAACL,SAAS,CAAC,GAAG,CAAA,EAAG,IAAI,CAAClB,QAAQ,CAAC8B,qBAAqB,EAAE,CAACZ,SAAS,CAAC,CAAA,EAAA,CAAI;EAExFa,IAAAA,eAAM,CAAC,IAAI,CAAC/B,QAAQ,CAAC;MAErB,IAAI,CAACA,QAAQ,CAACoB,SAAS,CAACE,GAAG,CAAClD,qBAAqB,CAAC;MAClD,IAAI,CAAC4B,QAAQ,CAACoB,SAAS,CAACC,MAAM,CAAClD,mBAAmB,EAAED,eAAe,CAAC;EAEpE,IAAA,KAAK,MAAM6C,OAAO,IAAI,IAAI,CAACzB,aAAa,EAAE;EACxC,MAAA,MAAMH,OAAO,GAAGK,cAAc,CAACwC,sBAAsB,CAACjB,OAAO,CAAC;QAE9D,IAAI5B,OAAO,IAAI,CAAC,IAAI,CAACmB,QAAQ,CAACnB,OAAO,CAAC,EAAE;UACtC,IAAI,CAACkB,yBAAyB,CAAC,CAACU,OAAO,CAAC,EAAE,KAAK,CAAC;EAClD,MAAA;EACF,IAAA;MAEA,IAAI,CAAC1B,gBAAgB,GAAG,IAAI;MAE5B,MAAMmC,QAAQ,GAAGA,MAAM;QACrB,IAAI,CAACnC,gBAAgB,GAAG,KAAK;QAC7B,IAAI,CAACW,QAAQ,CAACoB,SAAS,CAACC,MAAM,CAACjD,qBAAqB,CAAC;QACrD,IAAI,CAAC4B,QAAQ,CAACoB,SAAS,CAACE,GAAG,CAACnD,mBAAmB,CAAC;QAChD2C,YAAY,CAACC,OAAO,CAAC,IAAI,CAACf,QAAQ,EAAEhC,YAAY,CAAC;MACnD,CAAC;MAED,IAAI,CAACgC,QAAQ,CAACuB,KAAK,CAACL,SAAS,CAAC,GAAG,EAAE;MAEnC,IAAI,CAACW,cAAc,CAACL,QAAQ,EAAE,IAAI,CAACxB,QAAQ,EAAE,IAAI,CAAC;EACpD,EAAA;;EAEA;EACAM,EAAAA,QAAQA,CAACnB,OAAO,GAAG,IAAI,CAACa,QAAQ,EAAE;EAChC,IAAA,OAAOb,OAAO,CAACiC,SAAS,CAACa,QAAQ,CAAC/D,eAAe,CAAC;EACpD,EAAA;IAEAgE,iBAAiBA,CAAC9C,MAAM,EAAE;MACxBA,MAAM,CAACN,MAAM,GAAGqD,OAAO,CAAC/C,MAAM,CAACN,MAAM,CAAC,CAAA;MACtCM,MAAM,CAACP,MAAM,GAAGuD,mBAAU,CAAChD,MAAM,CAACP,MAAM,CAAC;EACzC,IAAA,OAAOO,MAAM;EACf,EAAA;EAEA+B,EAAAA,aAAaA,GAAG;EACd,IAAA,OAAO,IAAI,CAACnB,QAAQ,CAACoB,SAAS,CAACa,QAAQ,CAAC1D,qBAAqB,CAAC,GAAGC,KAAK,GAAGC,MAAM;EACjF,EAAA;EAEA0B,EAAAA,mBAAmBA,GAAG;EACpB,IAAA,IAAI,CAAC,IAAI,CAACC,OAAO,CAACvB,MAAM,EAAE;EACxB,MAAA;EACF,IAAA;EAEA,IAAA,MAAMwD,QAAQ,GAAG,IAAI,CAAC3B,sBAAsB,CAAC/B,oBAAoB,CAAC;EAElE,IAAA,KAAK,MAAMQ,OAAO,IAAIkD,QAAQ,EAAE;EAC9B,MAAA,MAAMC,QAAQ,GAAG9C,cAAc,CAACwC,sBAAsB,CAAC7C,OAAO,CAAC;EAE/D,MAAA,IAAImD,QAAQ,EAAE;EACZ,QAAA,IAAI,CAACjC,yBAAyB,CAAC,CAAClB,OAAO,CAAC,EAAE,IAAI,CAACmB,QAAQ,CAACgC,QAAQ,CAAC,CAAC;EACpE,MAAA;EACF,IAAA;EACF,EAAA;IAEA5B,sBAAsBA,CAACf,QAAQ,EAAE;EAC/B,IAAA,MAAM0C,QAAQ,GAAG7C,cAAc,CAACC,IAAI,CAACnB,0BAA0B,EAAE,IAAI,CAAC8B,OAAO,CAACvB,MAAM,CAAC;EACrF;MACA,OAAOW,cAAc,CAACC,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACS,OAAO,CAACvB,MAAM,CAAC,CAACiB,MAAM,CAACX,OAAO,IAAI,CAACkD,QAAQ,CAACE,QAAQ,CAACpD,OAAO,CAAC,CAAC;EAC1G,EAAA;EAEAkB,EAAAA,yBAAyBA,CAACmC,YAAY,EAAEC,MAAM,EAAE;EAC9C,IAAA,IAAI,CAACD,YAAY,CAACvC,MAAM,EAAE;EACxB,MAAA;EACF,IAAA;EAEA,IAAA,KAAK,MAAMd,OAAO,IAAIqD,YAAY,EAAE;QAClCrD,OAAO,CAACiC,SAAS,CAACtC,MAAM,CAACT,oBAAoB,EAAE,CAACoE,MAAM,CAAC;EACvDtD,MAAAA,OAAO,CAACuD,YAAY,CAAC,eAAe,EAAED,MAAM,CAAC;EAC/C,IAAA;EACF,EAAA;;EAEA;IACA,OAAOE,eAAeA,CAACvD,MAAM,EAAE;MAC7B,MAAMgB,OAAO,GAAG,EAAE;MAClB,IAAI,OAAOhB,MAAM,KAAK,QAAQ,IAAI,WAAW,CAACwD,IAAI,CAACxD,MAAM,CAAC,EAAE;QAC1DgB,OAAO,CAACtB,MAAM,GAAG,KAAK;EACxB,IAAA;EAEA,IAAA,OAAO,IAAI,CAAC+D,IAAI,CAAC,YAAY;QAC3B,MAAMC,IAAI,GAAG9D,QAAQ,CAAC4B,mBAAmB,CAAC,IAAI,EAAER,OAAO,CAAC;EAExD,MAAA,IAAI,OAAOhB,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA,IAAI,OAAO0D,IAAI,CAAC1D,MAAM,CAAC,KAAK,WAAW,EAAE;EACvC,UAAA,MAAM,IAAI2D,SAAS,CAAC,CAAA,iBAAA,EAAoB3D,MAAM,GAAG,CAAC;EACpD,QAAA;EAEA0D,QAAAA,IAAI,CAAC1D,MAAM,CAAC,EAAE;EAChB,MAAA;EACF,IAAA,CAAC,CAAC;EACJ,EAAA;EACF;;EAEA;EACA;EACA;;EAEA0B,YAAY,CAACkC,EAAE,CAACC,QAAQ,EAAEhF,oBAAoB,EAAEU,oBAAoB,EAAE,UAAUuE,KAAK,EAAE;EACrF;EACA,EAAA,IAAIA,KAAK,CAACC,MAAM,CAACC,OAAO,KAAK,GAAG,IAAKF,KAAK,CAACG,cAAc,IAAIH,KAAK,CAACG,cAAc,CAACD,OAAO,KAAK,GAAI,EAAE;MAClGF,KAAK,CAACI,cAAc,EAAE;EACxB,EAAA;IAEA,KAAK,MAAMnE,OAAO,IAAIK,cAAc,CAAC+D,+BAA+B,CAAC,IAAI,CAAC,EAAE;EAC1EvE,IAAAA,QAAQ,CAAC4B,mBAAmB,CAACzB,OAAO,EAAE;EAAEL,MAAAA,MAAM,EAAE;EAAM,KAAC,CAAC,CAACA,MAAM,EAAE;EACnE,EAAA;EACF,CAAC,CAAC;;EAEF;EACA;EACA;;AAEA0E,6BAAkB,CAACxE,QAAQ,CAAC;;;;;;;;"}