{"version": 3, "file": "backdrop.js", "sources": ["../../src/util/backdrop.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler.js'\nimport Config from './config.js'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n"], "names": ["NAME", "CLASS_NAME_FADE", "CLASS_NAME_SHOW", "EVENT_MOUSEDOWN", "<PERSON><PERSON><PERSON>", "className", "clickCallback", "isAnimated", "isVisible", "rootElement", "DefaultType", "Backdrop", "Config", "constructor", "config", "_config", "_getConfig", "_isAppended", "_element", "show", "callback", "execute", "_append", "element", "_getElement", "reflow", "classList", "add", "_emulateAnimation", "hide", "remove", "dispose", "EventHandler", "off", "backdrop", "document", "createElement", "_configAfterMerge", "getElement", "append", "on", "executeAfterTransition"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAQA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,UAAU;EACvB,MAAMC,eAAe,GAAG,MAAM;EAC9B,MAAMC,eAAe,GAAG,MAAM;EAC9B,MAAMC,eAAe,GAAG,CAAA,aAAA,EAAgBH,IAAI,CAAA,CAAE;EAE9C,MAAMI,OAAO,GAAG;EACdC,EAAAA,SAAS,EAAE,gBAAgB;EAC3BC,EAAAA,aAAa,EAAE,IAAI;EACnBC,EAAAA,UAAU,EAAE,KAAK;EACjBC,EAAAA,SAAS,EAAE,IAAI;EAAE;IACjBC,WAAW,EAAE,MAAM;EACrB,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBL,EAAAA,SAAS,EAAE,QAAQ;EACnBC,EAAAA,aAAa,EAAE,iBAAiB;EAChCC,EAAAA,UAAU,EAAE,SAAS;EACrBC,EAAAA,SAAS,EAAE,SAAS;EACpBC,EAAAA,WAAW,EAAE;EACf,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,QAAQ,SAASC,MAAM,CAAC;IAC5BC,WAAWA,CAACC,MAAM,EAAE;EAClB,IAAA,KAAK,EAAE;MACP,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACF,MAAM,CAAC;MACtC,IAAI,CAACG,WAAW,GAAG,KAAK;MACxB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACtB,EAAA;;EAEA;IACA,WAAWd,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB,EAAA;IAEA,WAAWM,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB,EAAA;IAEA,WAAWV,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb,EAAA;;EAEA;IACAmB,IAAIA,CAACC,QAAQ,EAAE;EACb,IAAA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACP,SAAS,EAAE;QAC3Ba,gBAAO,CAACD,QAAQ,CAAC;EACjB,MAAA;EACF,IAAA;MAEA,IAAI,CAACE,OAAO,EAAE;EAEd,IAAA,MAAMC,OAAO,GAAG,IAAI,CAACC,WAAW,EAAE;EAClC,IAAA,IAAI,IAAI,CAACT,OAAO,CAACR,UAAU,EAAE;QAC3BkB,eAAM,CAACF,OAAO,CAAC;EACjB,IAAA;EAEAA,IAAAA,OAAO,CAACG,SAAS,CAACC,GAAG,CAACzB,eAAe,CAAC;MAEtC,IAAI,CAAC0B,iBAAiB,CAAC,MAAM;QAC3BP,gBAAO,CAACD,QAAQ,CAAC;EACnB,IAAA,CAAC,CAAC;EACJ,EAAA;IAEAS,IAAIA,CAACT,QAAQ,EAAE;EACb,IAAA,IAAI,CAAC,IAAI,CAACL,OAAO,CAACP,SAAS,EAAE;QAC3Ba,gBAAO,CAACD,QAAQ,CAAC;EACjB,MAAA;EACF,IAAA;MAEA,IAAI,CAACI,WAAW,EAAE,CAACE,SAAS,CAACI,MAAM,CAAC5B,eAAe,CAAC;MAEpD,IAAI,CAAC0B,iBAAiB,CAAC,MAAM;QAC3B,IAAI,CAACG,OAAO,EAAE;QACdV,gBAAO,CAACD,QAAQ,CAAC;EACnB,IAAA,CAAC,CAAC;EACJ,EAAA;EAEAW,EAAAA,OAAOA,GAAG;EACR,IAAA,IAAI,CAAC,IAAI,CAACd,WAAW,EAAE;EACrB,MAAA;EACF,IAAA;MAEAe,YAAY,CAACC,GAAG,CAAC,IAAI,CAACf,QAAQ,EAAEf,eAAe,CAAC;EAEhD,IAAA,IAAI,CAACe,QAAQ,CAACY,MAAM,EAAE;MACtB,IAAI,CAACb,WAAW,GAAG,KAAK;EAC1B,EAAA;;EAEA;EACAO,EAAAA,WAAWA,GAAG;EACZ,IAAA,IAAI,CAAC,IAAI,CAACN,QAAQ,EAAE;EAClB,MAAA,MAAMgB,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAC9CF,MAAAA,QAAQ,CAAC7B,SAAS,GAAG,IAAI,CAACU,OAAO,CAACV,SAAS;EAC3C,MAAA,IAAI,IAAI,CAACU,OAAO,CAACR,UAAU,EAAE;EAC3B2B,QAAAA,QAAQ,CAACR,SAAS,CAACC,GAAG,CAAC1B,eAAe,CAAC;EACzC,MAAA;QAEA,IAAI,CAACiB,QAAQ,GAAGgB,QAAQ;EAC1B,IAAA;MAEA,OAAO,IAAI,CAAChB,QAAQ;EACtB,EAAA;IAEAmB,iBAAiBA,CAACvB,MAAM,EAAE;EACxB;MACAA,MAAM,CAACL,WAAW,GAAG6B,mBAAU,CAACxB,MAAM,CAACL,WAAW,CAAC;EACnD,IAAA,OAAOK,MAAM;EACf,EAAA;EAEAQ,EAAAA,OAAOA,GAAG;MACR,IAAI,IAAI,CAACL,WAAW,EAAE;EACpB,MAAA;EACF,IAAA;EAEA,IAAA,MAAMM,OAAO,GAAG,IAAI,CAACC,WAAW,EAAE;MAClC,IAAI,CAACT,OAAO,CAACN,WAAW,CAAC8B,MAAM,CAAChB,OAAO,CAAC;EAExCS,IAAAA,YAAY,CAACQ,EAAE,CAACjB,OAAO,EAAEpB,eAAe,EAAE,MAAM;EAC9CkB,MAAAA,gBAAO,CAAC,IAAI,CAACN,OAAO,CAACT,aAAa,CAAC;EACrC,IAAA,CAAC,CAAC;MAEF,IAAI,CAACW,WAAW,GAAG,IAAI;EACzB,EAAA;IAEAW,iBAAiBA,CAACR,QAAQ,EAAE;EAC1BqB,IAAAA,+BAAsB,CAACrB,QAAQ,EAAE,IAAI,CAACI,WAAW,EAAE,EAAE,IAAI,CAACT,OAAO,CAACR,UAAU,CAAC;EAC/E,EAAA;EACF;;;;;;;;"}