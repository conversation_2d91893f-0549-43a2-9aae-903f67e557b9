{"version": 3, "file": "event-handler.js", "sources": ["../../src/dom/event-handler.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index.js'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n"], "names": ["namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "element", "uid", "getElementEvents", "bootstrapHandler", "fn", "handler", "event", "hydrateObj", "<PERSON><PERSON><PERSON><PERSON>", "oneOff", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "selector", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "parentNode", "dom<PERSON>lement", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "normalizeParameters", "originalTypeEvent", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "wrapFunction", "relatedTarget", "contains", "call", "handlers", "previousFunction", "replace", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "keys", "length", "elementEvent", "slice", "keyHandlers", "trigger", "args", "$", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "Event", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "dispatchEvent", "obj", "meta", "key", "value", "_unused", "defineProperty", "configurable", "get"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EAIA;EACA;EACA;;EAEA,MAAMA,cAAc,GAAG,oBAAoB;EAC3C,MAAMC,cAAc,GAAG,MAAM;EAC7B,MAAMC,aAAa,GAAG,QAAQ;EAC9B,MAAMC,aAAa,GAAG,EAAE,CAAA;EACxB,IAAIC,QAAQ,GAAG,CAAC;EAChB,MAAMC,YAAY,GAAG;EACnBC,EAAAA,UAAU,EAAE,WAAW;EACvBC,EAAAA,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAC3B,OAAO,EACP,UAAU,EACV,SAAS,EACT,WAAW,EACX,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,WAAW,EACX,UAAU,EACV,WAAW,EACX,aAAa,EACb,WAAW,EACX,SAAS,EACT,UAAU,EACV,OAAO,EACP,mBAAmB,EACnB,YAAY,EACZ,WAAW,EACX,UAAU,EACV,aAAa,EACb,aAAa,EACb,aAAa,EACb,WAAW,EACX,cAAc,EACd,eAAe,EACf,cAAc,EACd,eAAe,EACf,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,UAAU,EACV,MAAM,EACN,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,MAAM,EACN,kBAAkB,EAClB,kBAAkB,EAClB,OAAO,EACP,OAAO,EACP,QAAQ,CACT,CAAC;;EAEF;EACA;EACA;;EAEA,SAASC,YAAYA,CAACC,OAAO,EAAEC,GAAG,EAAE;EAClC,EAAA,OAAQA,GAAG,IAAI,CAAA,EAAGA,GAAG,KAAKR,QAAQ,EAAE,CAAA,CAAE,IAAKO,OAAO,CAACP,QAAQ,IAAIA,QAAQ,EAAE;EAC3E;EAEA,SAASS,gBAAgBA,CAACF,OAAO,EAAE;EACjC,EAAA,MAAMC,GAAG,GAAGF,YAAY,CAACC,OAAO,CAAC;IAEjCA,OAAO,CAACP,QAAQ,GAAGQ,GAAG;IACtBT,aAAa,CAACS,GAAG,CAAC,GAAGT,aAAa,CAACS,GAAG,CAAC,IAAI,EAAE;IAE7C,OAAOT,aAAa,CAACS,GAAG,CAAC;EAC3B;EAEA,SAASE,gBAAgBA,CAACH,OAAO,EAAEI,EAAE,EAAE;EACrC,EAAA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;MAC7BC,UAAU,CAACD,KAAK,EAAE;EAAEE,MAAAA,cAAc,EAAER;EAAQ,KAAC,CAAC;MAE9C,IAAIK,OAAO,CAACI,MAAM,EAAE;QAClBC,YAAY,CAACC,GAAG,CAACX,OAAO,EAAEM,KAAK,CAACM,IAAI,EAAER,EAAE,CAAC;EAC3C,IAAA;MAEA,OAAOA,EAAE,CAACS,KAAK,CAACb,OAAO,EAAE,CAACM,KAAK,CAAC,CAAC;IACnC,CAAC;EACH;EAEA,SAASQ,0BAA0BA,CAACd,OAAO,EAAEe,QAAQ,EAAEX,EAAE,EAAE;EACzD,EAAA,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,IAAA,MAAMU,WAAW,GAAGhB,OAAO,CAACiB,gBAAgB,CAACF,QAAQ,CAAC;EAEtD,IAAA,KAAK,IAAI;EAAEG,MAAAA;EAAO,KAAC,GAAGZ,KAAK,EAAEY,MAAM,IAAIA,MAAM,KAAK,IAAI,EAAEA,MAAM,GAAGA,MAAM,CAACC,UAAU,EAAE;EAClF,MAAA,KAAK,MAAMC,UAAU,IAAIJ,WAAW,EAAE;UACpC,IAAII,UAAU,KAAKF,MAAM,EAAE;EACzB,UAAA;EACF,QAAA;UAEAX,UAAU,CAACD,KAAK,EAAE;EAAEE,UAAAA,cAAc,EAAEU;EAAO,SAAC,CAAC;UAE7C,IAAIb,OAAO,CAACI,MAAM,EAAE;EAClBC,UAAAA,YAAY,CAACC,GAAG,CAACX,OAAO,EAAEM,KAAK,CAACM,IAAI,EAAEG,QAAQ,EAAEX,EAAE,CAAC;EACrD,QAAA;UAEA,OAAOA,EAAE,CAACS,KAAK,CAACK,MAAM,EAAE,CAACZ,KAAK,CAAC,CAAC;EAClC,MAAA;EACF,IAAA;IACF,CAAC;EACH;EAEA,SAASe,WAAWA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,kBAAkB,GAAG,IAAI,EAAE;IAChE,OAAOC,MAAM,CAACC,MAAM,CAACJ,MAAM,CAAC,CACzBK,IAAI,CAACrB,KAAK,IAAIA,KAAK,CAACiB,QAAQ,KAAKA,QAAQ,IAAIjB,KAAK,CAACkB,kBAAkB,KAAKA,kBAAkB,CAAC;EAClG;EAEA,SAASI,mBAAmBA,CAACC,iBAAiB,EAAExB,OAAO,EAAEyB,kBAAkB,EAAE;EAC3E,EAAA,MAAMC,WAAW,GAAG,OAAO1B,OAAO,KAAK,QAAQ;EAC/C;IACA,MAAMkB,QAAQ,GAAGQ,WAAW,GAAGD,kBAAkB,GAAIzB,OAAO,IAAIyB,kBAAmB;EACnF,EAAA,IAAIE,SAAS,GAAGC,YAAY,CAACJ,iBAAiB,CAAC;EAE/C,EAAA,IAAI,CAAChC,YAAY,CAACqC,GAAG,CAACF,SAAS,CAAC,EAAE;EAChCA,IAAAA,SAAS,GAAGH,iBAAiB;EAC/B,EAAA;EAEA,EAAA,OAAO,CAACE,WAAW,EAAER,QAAQ,EAAES,SAAS,CAAC;EAC3C;EAEA,SAASG,UAAUA,CAACnC,OAAO,EAAE6B,iBAAiB,EAAExB,OAAO,EAAEyB,kBAAkB,EAAErB,MAAM,EAAE;EACnF,EAAA,IAAI,OAAOoB,iBAAiB,KAAK,QAAQ,IAAI,CAAC7B,OAAO,EAAE;EACrD,IAAA;EACF,EAAA;EAEA,EAAA,IAAI,CAAC+B,WAAW,EAAER,QAAQ,EAAES,SAAS,CAAC,GAAGJ,mBAAmB,CAACC,iBAAiB,EAAExB,OAAO,EAAEyB,kBAAkB,CAAC;;EAE5G;EACA;IACA,IAAID,iBAAiB,IAAInC,YAAY,EAAE;MACrC,MAAM0C,YAAY,GAAGhC,EAAE,IAAI;QACzB,OAAO,UAAUE,KAAK,EAAE;UACtB,IAAI,CAACA,KAAK,CAAC+B,aAAa,IAAK/B,KAAK,CAAC+B,aAAa,KAAK/B,KAAK,CAACE,cAAc,IAAI,CAACF,KAAK,CAACE,cAAc,CAAC8B,QAAQ,CAAChC,KAAK,CAAC+B,aAAa,CAAE,EAAE;EACjI,UAAA,OAAOjC,EAAE,CAACmC,IAAI,CAAC,IAAI,EAAEjC,KAAK,CAAC;EAC7B,QAAA;QACF,CAAC;MACH,CAAC;EAEDiB,IAAAA,QAAQ,GAAGa,YAAY,CAACb,QAAQ,CAAC;EACnC,EAAA;EAEA,EAAA,MAAMD,MAAM,GAAGpB,gBAAgB,CAACF,OAAO,CAAC;EACxC,EAAA,MAAMwC,QAAQ,GAAGlB,MAAM,CAACU,SAAS,CAAC,KAAKV,MAAM,CAACU,SAAS,CAAC,GAAG,EAAE,CAAC;EAC9D,EAAA,MAAMS,gBAAgB,GAAGpB,WAAW,CAACmB,QAAQ,EAAEjB,QAAQ,EAAEQ,WAAW,GAAG1B,OAAO,GAAG,IAAI,CAAC;EAEtF,EAAA,IAAIoC,gBAAgB,EAAE;EACpBA,IAAAA,gBAAgB,CAAChC,MAAM,GAAGgC,gBAAgB,CAAChC,MAAM,IAAIA,MAAM;EAE3D,IAAA;EACF,EAAA;EAEA,EAAA,MAAMR,GAAG,GAAGF,YAAY,CAACwB,QAAQ,EAAEM,iBAAiB,CAACa,OAAO,CAACrD,cAAc,EAAE,EAAE,CAAC,CAAC;EACjF,EAAA,MAAMe,EAAE,GAAG2B,WAAW,GACpBjB,0BAA0B,CAACd,OAAO,EAAEK,OAAO,EAAEkB,QAAQ,CAAC,GACtDpB,gBAAgB,CAACH,OAAO,EAAEuB,QAAQ,CAAC;EAErCnB,EAAAA,EAAE,CAACoB,kBAAkB,GAAGO,WAAW,GAAG1B,OAAO,GAAG,IAAI;IACpDD,EAAE,CAACmB,QAAQ,GAAGA,QAAQ;IACtBnB,EAAE,CAACK,MAAM,GAAGA,MAAM;IAClBL,EAAE,CAACX,QAAQ,GAAGQ,GAAG;EACjBuC,EAAAA,QAAQ,CAACvC,GAAG,CAAC,GAAGG,EAAE;IAElBJ,OAAO,CAAC2C,gBAAgB,CAACX,SAAS,EAAE5B,EAAE,EAAE2B,WAAW,CAAC;EACtD;EAEA,SAASa,aAAaA,CAAC5C,OAAO,EAAEsB,MAAM,EAAEU,SAAS,EAAE3B,OAAO,EAAEmB,kBAAkB,EAAE;EAC9E,EAAA,MAAMpB,EAAE,GAAGiB,WAAW,CAACC,MAAM,CAACU,SAAS,CAAC,EAAE3B,OAAO,EAAEmB,kBAAkB,CAAC;IAEtE,IAAI,CAACpB,EAAE,EAAE;EACP,IAAA;EACF,EAAA;IAEAJ,OAAO,CAAC6C,mBAAmB,CAACb,SAAS,EAAE5B,EAAE,EAAE0C,OAAO,CAACtB,kBAAkB,CAAC,CAAC;IACvE,OAAOF,MAAM,CAACU,SAAS,CAAC,CAAC5B,EAAE,CAACX,QAAQ,CAAC;EACvC;EAEA,SAASsD,wBAAwBA,CAAC/C,OAAO,EAAEsB,MAAM,EAAEU,SAAS,EAAEgB,SAAS,EAAE;IACvE,MAAMC,iBAAiB,GAAG3B,MAAM,CAACU,SAAS,CAAC,IAAI,EAAE;EAEjD,EAAA,KAAK,MAAM,CAACkB,UAAU,EAAE5C,KAAK,CAAC,IAAImB,MAAM,CAAC0B,OAAO,CAACF,iBAAiB,CAAC,EAAE;EACnE,IAAA,IAAIC,UAAU,CAACE,QAAQ,CAACJ,SAAS,CAAC,EAAE;EAClCJ,MAAAA,aAAa,CAAC5C,OAAO,EAAEsB,MAAM,EAAEU,SAAS,EAAE1B,KAAK,CAACiB,QAAQ,EAAEjB,KAAK,CAACkB,kBAAkB,CAAC;EACrF,IAAA;EACF,EAAA;EACF;EAEA,SAASS,YAAYA,CAAC3B,KAAK,EAAE;EAC3B;IACAA,KAAK,GAAGA,KAAK,CAACoC,OAAO,CAACpD,cAAc,EAAE,EAAE,CAAC;EACzC,EAAA,OAAOI,YAAY,CAACY,KAAK,CAAC,IAAIA,KAAK;EACrC;AAEA,QAAMI,YAAY,GAAG;IACnB2C,EAAEA,CAACrD,OAAO,EAAEM,KAAK,EAAED,OAAO,EAAEyB,kBAAkB,EAAE;MAC9CK,UAAU,CAACnC,OAAO,EAAEM,KAAK,EAAED,OAAO,EAAEyB,kBAAkB,EAAE,KAAK,CAAC;IAChE,CAAC;IAEDwB,GAAGA,CAACtD,OAAO,EAAEM,KAAK,EAAED,OAAO,EAAEyB,kBAAkB,EAAE;MAC/CK,UAAU,CAACnC,OAAO,EAAEM,KAAK,EAAED,OAAO,EAAEyB,kBAAkB,EAAE,IAAI,CAAC;IAC/D,CAAC;IAEDnB,GAAGA,CAACX,OAAO,EAAE6B,iBAAiB,EAAExB,OAAO,EAAEyB,kBAAkB,EAAE;EAC3D,IAAA,IAAI,OAAOD,iBAAiB,KAAK,QAAQ,IAAI,CAAC7B,OAAO,EAAE;EACrD,MAAA;EACF,IAAA;EAEA,IAAA,MAAM,CAAC+B,WAAW,EAAER,QAAQ,EAAES,SAAS,CAAC,GAAGJ,mBAAmB,CAACC,iBAAiB,EAAExB,OAAO,EAAEyB,kBAAkB,CAAC;EAC9G,IAAA,MAAMyB,WAAW,GAAGvB,SAAS,KAAKH,iBAAiB;EACnD,IAAA,MAAMP,MAAM,GAAGpB,gBAAgB,CAACF,OAAO,CAAC;MACxC,MAAMiD,iBAAiB,GAAG3B,MAAM,CAACU,SAAS,CAAC,IAAI,EAAE;EACjD,IAAA,MAAMwB,WAAW,GAAG3B,iBAAiB,CAAC4B,UAAU,CAAC,GAAG,CAAC;EAErD,IAAA,IAAI,OAAOlC,QAAQ,KAAK,WAAW,EAAE;EACnC;QACA,IAAI,CAACE,MAAM,CAACiC,IAAI,CAACT,iBAAiB,CAAC,CAACU,MAAM,EAAE;EAC1C,QAAA;EACF,MAAA;EAEAf,MAAAA,aAAa,CAAC5C,OAAO,EAAEsB,MAAM,EAAEU,SAAS,EAAET,QAAQ,EAAEQ,WAAW,GAAG1B,OAAO,GAAG,IAAI,CAAC;EACjF,MAAA;EACF,IAAA;EAEA,IAAA,IAAImD,WAAW,EAAE;QACf,KAAK,MAAMI,YAAY,IAAInC,MAAM,CAACiC,IAAI,CAACpC,MAAM,CAAC,EAAE;EAC9CyB,QAAAA,wBAAwB,CAAC/C,OAAO,EAAEsB,MAAM,EAAEsC,YAAY,EAAE/B,iBAAiB,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC;EACrF,MAAA;EACF,IAAA;EAEA,IAAA,KAAK,MAAM,CAACC,WAAW,EAAExD,KAAK,CAAC,IAAImB,MAAM,CAAC0B,OAAO,CAACF,iBAAiB,CAAC,EAAE;QACpE,MAAMC,UAAU,GAAGY,WAAW,CAACpB,OAAO,CAACnD,aAAa,EAAE,EAAE,CAAC;QAEzD,IAAI,CAACgE,WAAW,IAAI1B,iBAAiB,CAACuB,QAAQ,CAACF,UAAU,CAAC,EAAE;EAC1DN,QAAAA,aAAa,CAAC5C,OAAO,EAAEsB,MAAM,EAAEU,SAAS,EAAE1B,KAAK,CAACiB,QAAQ,EAAEjB,KAAK,CAACkB,kBAAkB,CAAC;EACrF,MAAA;EACF,IAAA;IACF,CAAC;EAEDuC,EAAAA,OAAOA,CAAC/D,OAAO,EAAEM,KAAK,EAAE0D,IAAI,EAAE;EAC5B,IAAA,IAAI,OAAO1D,KAAK,KAAK,QAAQ,IAAI,CAACN,OAAO,EAAE;EACzC,MAAA,OAAO,IAAI;EACb,IAAA;EAEA,IAAA,MAAMiE,CAAC,GAAGC,kBAAS,EAAE;EACrB,IAAA,MAAMlC,SAAS,GAAGC,YAAY,CAAC3B,KAAK,CAAC;EACrC,IAAA,MAAMiD,WAAW,GAAGjD,KAAK,KAAK0B,SAAS;MAEvC,IAAImC,WAAW,GAAG,IAAI;MACtB,IAAIC,OAAO,GAAG,IAAI;MAClB,IAAIC,cAAc,GAAG,IAAI;MACzB,IAAIC,gBAAgB,GAAG,KAAK;MAE5B,IAAIf,WAAW,IAAIU,CAAC,EAAE;QACpBE,WAAW,GAAGF,CAAC,CAACM,KAAK,CAACjE,KAAK,EAAE0D,IAAI,CAAC;EAElCC,MAAAA,CAAC,CAACjE,OAAO,CAAC,CAAC+D,OAAO,CAACI,WAAW,CAAC;EAC/BC,MAAAA,OAAO,GAAG,CAACD,WAAW,CAACK,oBAAoB,EAAE;EAC7CH,MAAAA,cAAc,GAAG,CAACF,WAAW,CAACM,6BAA6B,EAAE;EAC7DH,MAAAA,gBAAgB,GAAGH,WAAW,CAACO,kBAAkB,EAAE;EACrD,IAAA;MAEA,MAAMC,GAAG,GAAGpE,UAAU,CAAC,IAAIgE,KAAK,CAACjE,KAAK,EAAE;QAAE8D,OAAO;EAAEQ,MAAAA,UAAU,EAAE;OAAM,CAAC,EAAEZ,IAAI,CAAC;EAE7E,IAAA,IAAIM,gBAAgB,EAAE;QACpBK,GAAG,CAACE,cAAc,EAAE;EACtB,IAAA;EAEA,IAAA,IAAIR,cAAc,EAAE;EAClBrE,MAAAA,OAAO,CAAC8E,aAAa,CAACH,GAAG,CAAC;EAC5B,IAAA;EAEA,IAAA,IAAIA,GAAG,CAACL,gBAAgB,IAAIH,WAAW,EAAE;QACvCA,WAAW,CAACU,cAAc,EAAE;EAC9B,IAAA;EAEA,IAAA,OAAOF,GAAG;EACZ,EAAA;EACF;EAEA,SAASpE,UAAUA,CAACwE,GAAG,EAAEC,IAAI,GAAG,EAAE,EAAE;EAClC,EAAA,KAAK,MAAM,CAACC,GAAG,EAAEC,KAAK,CAAC,IAAIzD,MAAM,CAAC0B,OAAO,CAAC6B,IAAI,CAAC,EAAE;MAC/C,IAAI;EACFD,MAAAA,GAAG,CAACE,GAAG,CAAC,GAAGC,KAAK;MAClB,CAAC,CAAC,OAAAC,OAAA,EAAM;EACN1D,MAAAA,MAAM,CAAC2D,cAAc,CAACL,GAAG,EAAEE,GAAG,EAAE;EAC9BI,QAAAA,YAAY,EAAE,IAAI;EAClBC,QAAAA,GAAGA,GAAG;EACJ,UAAA,OAAOJ,KAAK;EACd,QAAA;EACF,OAAC,CAAC;EACJ,IAAA;EACF,EAAA;EAEA,EAAA,OAAOH,GAAG;EACZ;;;;;;;;"}