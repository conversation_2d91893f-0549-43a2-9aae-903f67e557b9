import React, { useState } from "react";
import "./App.css";

export default function DownloadBill({ showList }) {
  const [refNumber, setRefNumber] = useState("");
  const [status, setStatus] = useState("");
  const [statusList, setStatusList] = useState([]);
  const [loadingFile, setLoadingFile] = useState(false);
  const [uploadStatus, setUploadStatus] = useState("");
  const [totalRefs, setTotalRefs] = useState(null);

  // ✅ Download single bill
  const downloadBill = async () => {
    if (!refNumber.trim()) {
      setStatus("❌ Please enter a reference number!");
      return;
    }
    setStatus("⏳ Downloading...");
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-single", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ refNumber }),
      });
      const data = await res.json();
      if (res.ok) {
        setStatus(
          `✅ ${data.message || "Bill downloaded successfully"} → ` +
            (data.file
              ? `👉 [Download here](${data.file})`
              : "⚠️ File link missing")
        );
      } else {
        setStatus(`❌ ${data.error || data.detail || "Failed to download bill"}`);
      }
    } catch {
      setStatus("❌ Error connecting to server");
    }
  };

  // ✅ Download bills from uploaded file
  const downloadAllBills = async () => {
    setLoadingFile(true);
    setStatusList([]);
    try {
      const res = await fetch("http://127.0.0.1:8000/api/run-file");
      const data = await res.json();
      if (res.ok) {
        setStatusList(data.results || []);
      } else {
        setStatusList([
          {
            refNumber: "-",
            status: `❌ ${data.error || data.detail || "Failed to process file"}`,
          },
        ]);
      }
    } catch {
      setStatusList([{ refNumber: "-", status: "❌ Error connecting to server" }]);
    } finally {
      setLoadingFile(false);
    }
  };

  // ✅ Handle Excel / ODS file upload
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;

    if (!/\.(xlsx|xls|ods)$/i.test(file.name)) {
      setUploadStatus("❌ Only Excel files (.xlsx, .xls, .ods) are allowed");
      return;
    }

    setUploadStatus("⏳ Uploading...");
    const formData = new FormData();
    formData.append("file", file);

    try {
      const res = await fetch("http://127.0.0.1:8000/api/upload-excel", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      if (res.ok) {
        setUploadStatus(`✅ ${data.message || "File uploaded successfully"}`);
        setTotalRefs(data.total_refs || null);

        // 🔥 Auto-run download after upload
        await downloadAllBills();
      } else {
        setUploadStatus(`❌ ${data.error || data.detail || "Upload failed"}`);
      }
    } catch {
      setUploadStatus("❌ Error connecting to server");
    }
  };

  return (
    <div className="card p-4 shadow-sm">
      {/* Single Bill Section */}
      <h3 className="text-center mb-3 text-dark">Download Single Bill</h3>
      <div className="input-group mb-3">
        <input
          type="text"
          className="form-control"
          placeholder="Enter Reference Number"
          value={refNumber}
          onChange={(e) => setRefNumber(e.target.value)}
        />
        <button
          style={{ backgroundColor: "#714B67", color: "white" }}
          className="btn"
          onClick={downloadBill}
        >
          Download
        </button>
      </div>
      {status && (
        <p
          className="fw-bold"
          dangerouslySetInnerHTML={{ __html: status.replace(/\n/g, "<br/>") }}
        />
      )}

      <hr />

      {/* Upload Excel Section */}
      <h3 className="text-center mb-3 text-dark">Upload Excel / ODS File</h3>
      <div className="mb-3">
        <input
          type="file"
          className="form-control"
          accept=".xlsx, .xls, .ods"
          onChange={handleFileUpload}
        />
      </div>
      {uploadStatus && <p className="fw-bold">{uploadStatus}</p>}
      {totalRefs !== null && (
        <p className="fw-bold">📊 Total Reference Numbers: {totalRefs}</p>
      )}

      <hr />

      {/* File Download Section */}
      <h3 className="text-center mb-3 text-dark">Download Bills from File</h3>
      <div className="d-flex justify-content-center mb-3">
        <button
          style={{ backgroundColor: "#714B67", color: "white" }}
          className="btn"
          onClick={downloadAllBills}
          disabled={loadingFile}
        >
          {loadingFile ? "⏳ Downloading..." : "Download All Bills"}
        </button>
        <button
          style={{ backgroundColor: "#714B67", color: "white" }}
          className="btn ms-2"
          onClick={showList}
        >
          View List
        </button>
      </div>

      {/* Status Table */}
      {statusList.length > 0 && (
        <table className="table table-bordered table-striped mt-3">
          <thead className="table-dark">
            <tr>
              <th>Reference Number</th>
              <th>Status</th>
              <th>File</th>
            </tr>
          </thead>
          <tbody>
            {statusList.map((item, idx) => {
              const success = item.status.startsWith("✅");
              return (
                <tr key={idx}>
                  <td>{item.refNumber}</td>
                  <td>
                    <span
                      className={`badge ${
                        success ? "bg-success" : "bg-danger"
                      }`}
                    >
                      {item.status}
                    </span>
                  </td>
                  <td>
                    {item.file ? (
                      <a
                        href={`http://127.0.0.1:8000${item.file}`}
                        target="_blank"
                        rel="noreferrer"
                      >
                        📥 Download
                      </a>
                    ) : (
                      "—"
                    )}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      )}
    </div>
  );
}
