/* App.css */

/* Background + global font */
body {
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
  background: #f8f9fa !important;
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
}

/* App title */
.app-title {
  font-size: 2rem;
  font-weight: 600;
  color: #343a40;
}

/* Logo */
.logo {
  max-width: 180px;
  height: auto;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Card */
.card {
  border: none;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1) !important;
  background: #ffffff;
}

/* Buttons */
button {
  border-radius: 8px !important;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}
