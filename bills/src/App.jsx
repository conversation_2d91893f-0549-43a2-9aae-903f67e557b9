import React, { useState } from "react";
import DownloadBill from "./DownloadBill";
import BillList from "./BillList";
import "bootstrap/dist/css/bootstrap.min.css";


function App() {
  const [viewList, setViewList] = useState(false);

  return (
    <div className="d-flex justify-content-center align-items-center vh-100">
      <div className="card shadow-sm p-4 text-center" style={{ maxWidth: "650px", width: "100%" }}>
        {/* Header */}
        <div className="app-header mb-4">
          <img
            src="/teletaleem.jpeg"
            alt="Tele Taleem Logo"
            className="logo mb-3"
          />
          <h1 className="app-title">Tele Taleem PowerBill</h1>
        </div>

        {/* Content */}
        {!viewList ? (
          <DownloadBill showList={() => setViewList(true)} />
        ) : (
          <BillList goBack={() => setViewList(false)} />
        )}
      </div>
    </div>
  );
}

export default App;
